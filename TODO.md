重要：钓点详情页面
重要：一起钓鱼页面
重要：钓鱼动态页面
重要：钓点编辑修改页面
重要：聊天页面待完善
重要：探索页面还无法工作
头像无法显示
图片无法放大缩小
剪贴板里有钓点无法清楚导致每次启动都提示。
程序启动时的黑屏时间太长

SpotAccessCache只在定义文件中出现，没有在任何其他地方被引用或使用

解决后端ip地址硬编码的问题，使用这两个方式获取后端ip：
curl -s "https://doh.pub/dns-query?name=app.19840112.xyz&type=A"
curl -s "https://dns.alidns.com/resolve?name=app.19840112.xyz&type=A"

优化各个页面从后端数据库加载的内容。地图页面的标签，只需要从数据库请求位置坐标，id ,图标，这三个信息。其他信息都不用。

增加一个搜索指示标记，搜索栏搜索到地址后，点击跳转，会显示这个地址标记，这个地址标记只有一个。

钓点详情页中的评论卡片，下滑评论时首先滑动评论列表，滑动到底后，应该能变成滑动评论页面。上滑也一样。

钓点类型增加手竿，路亚

bug： 实地标签一开始会亮。移动一下才会判断是否是实地钓点。

给发布设置一个超时，


第一阶段：立即更新（核心功能修复）
修复活动显示问题
创建独立的活动标记组件，区别于钓点标记
实现活动类型的视觉差异化（方形标记 vs 圆形钓点标记）
确保无照片活动也能正常显示
优化活动数据加载
修改地图页面的活动加载逻辑，支持地理范围过滤
统一钓点和活动的加载策略
第二阶段：系统性升级（在第一阶段测试完成后）
实现完整的过滤系统
按照技术文档实现FilterService、CacheManager、DataLoader
添加过滤面板UI组件
实现智能缓存和批量渲染
性能优化和监控
实现标记池化管理
添加性能监控
优化内存使用
具体实施步骤
立即更新部分：

创建ActivityMarker组件（独立于钓点标记）
修改地图页面的活动加载和显示逻辑
更新活动服务支持地理范围查询
测试活动发布和显示功能
后续更新部分：

实现FilterService和相关过滤组件
重构数据加载架构
添加性能优化和监控
全面测试和优化
这个方案确保了：

立即解决用户反馈的核心问题（活动无法显示）
为后续的系统性升级奠定基础
分阶段实施，降低风险
符合手机app的最佳实践