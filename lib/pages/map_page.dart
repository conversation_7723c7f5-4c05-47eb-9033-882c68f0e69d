import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:flutter_map_location_marker/flutter_map_location_marker.dart';
import 'package:latlong2/latlong.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:async';
import 'dart:math' as math;
import '../services/service_locator.dart';
import '../services/map_navigation_service.dart';
import '../models/fishing_spot.dart';
import '../utils/map_coordinate_utils.dart';
import '../utils/marker_alignment_utils.dart';
import '../utils/tianditu_utils.dart';

import '../widgets/dev_menu.dart';
import '../widgets/split_screen_add_spot.dart';
import '../widgets/split_screen_add_activity.dart';
import '../widgets/photo_fishing_spot_marker.dart';
import '../widgets/activity_marker.dart';
import 'spot_detail_page.dart';
import '../widgets/optimized_search_bar.dart';
import '../widgets/snackbar.dart';
import '../models/fishing_activity.dart';
import '../config/app_config.dart';

class MapPage extends StatefulWidget {
  final Function(bool)? onBottomNavigationBarVisibilityChanged;

  const MapPage({super.key, this.onBottomNavigationBarVisibilityChanged});

  @override
  State<MapPage> createState() => _MapPageState();
}

class _MapPageState extends State<MapPage>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  // 天地图密钥，需要在天地图官网申请
  final MapController mapController = MapController();
  // 地图类型：true为矢量图，false为卫星图
  bool isVectorMap = false;

  // 活动数据版本号，用于追踪数据变化
  int _activitiesVersion = 0;
  // 是否显示注记层
  bool showAnnotationLayer = true;

  // 服务 - 使用新的服务架构
  // 通过Services便捷访问器访问服务

  // 数据
  final List<FishingSpot> _spots = [];
  final List<FishingActivity> _activities = [];
  LatLng _userLocation = const LatLng(39.9042, 116.4074); // 默认北京位置
  bool _isLoading = true;

  // 标记缓存 - 避免重复创建标记组件
  final Map<String, Widget> _spotMarkerCache = {};
  final Map<String, Widget> _activityMarkerCache = {};

  // 批量更新定时器
  Timer? _batchUpdateTimer;

  final GlobalKey _mapKey = GlobalKey();

  // 分屏添加模式状态
  bool _isSplitScreenMode = false;
  bool _isAddingActivity = false; // true为添加约钓活动，false为添加钓点
  LatLng _centerMarkerLocation = const LatLng(39.9042, 116.4074);
  String? _suggestedSpotName; // 建议的钓点名称

  // 保存进入分屏模式前的地图中心位置，用于退出时恢复
  LatLng? _originalMapCenter;

  // 定时位置更新
  Timer? _locationUpdateTimer;
  final bool _enablePeriodicLocationUpdate = true;
  static const Duration _locationUpdateInterval = Duration(seconds: 10);

  // 位置重置按钮状态
  bool _isLocationResetting = false;

  // 位置监听订阅
  StreamSubscription<LatLng>? _locationSubscription;

  // 搜索栏状态
  bool _isSearching = false;

  // 分屏模式动画控制器
  late AnimationController _splitScreenAnimationController;
  late Animation<double> _splitScreenAnimation;

  // 地图准备状态和待执行的导航请求
  bool _isMapReady = false;
  String? _pendingSpotId;
  LatLng? _pendingSpotLocation;

  // 统一导航相关 ⭐ 新增
  LatLng? _temporaryMarkerLocation;
  bool _showTemporaryMarker = false;
  MapNavigationParams? _pendingNavigationParams;

  // 搜索栏组件的全局键，用于控制搜索栏状态
  final GlobalKey<OptimizedSearchBarState> _searchBarKey =
      GlobalKey<OptimizedSearchBarState>();

  @override
  bool get wantKeepAlive => true; // 保持MapPage状态，避免应用恢复时重建

  @override
  void initState() {
    super.initState();

    // 注册统一导航回调 ⭐ 新增
    MapNavigationService.instance.registerMapNavigationCallback(
      _handleUnifiedNavigation,
    );

    // 初始化分屏动画控制器
    _splitScreenAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _splitScreenAnimation = CurvedAnimation(
      parent: _splitScreenAnimationController,
      curve: Curves.easeOutCubic,
    );

    // 检查位置服务
    _debugLocationServices();

    // 确保位置服务已启动
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _ensureLocationServiceStarted();
    });

    // 确保初始状态下导航栏是显示的
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && !_isSplitScreenMode) {
        widget.onBottomNavigationBarVisibilityChanged?.call(true);
      }
    });

    // 动画初始化已移除，如需要可重新添加

    _initializeData();

    // 监听地图移动，加载范围内的钓点
    mapController.mapEventStream.listen((event) {
      if (event is MapEventMoveEnd) {
        _loadSpotsInBounds();
      }
    });

    // 监听用户认证状态变化，当用户切换时重置地图数据
    Services.auth.currentUserNotifier.addListener(_onUserChanged);

    // 监听活动数据变化
    Services.fishingActivity.addListener(_onActivitiesDataChanged);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 不在这里刷新，避免过度刷新
  }

  /// 当从其他页面返回时检查是否需要刷新
  void checkForUpdatesOnReturn() {
    debugPrint('🔄 [地图页面] 从其他页面返回，检查更新');
    debugPrint('🔄 [地图页面] 当前地图上活动数量: ${_activities.length}');

    // 打印当前活动列表
    for (final activity in _activities) {
      debugPrint(
        '🔄 [地图页面] 当前活动: ${activity.id} - ${activity.title} (${activity.status})',
      );
    }

    // 强制刷新活动数据
    forceRefreshActivities();
  }

  @override
  void dispose() {
    // 注销统一导航回调 ⭐ 新增
    MapNavigationService.instance.unregisterMapNavigationCallback();

    // 清理定时器和监听器
    _locationUpdateTimer?.cancel();
    _locationSubscription?.cancel();
    _batchUpdateTimer?.cancel();

    // 清理动画控制器
    _splitScreenAnimationController.dispose();

    // 移除用户状态监听器
    Services.auth.currentUserNotifier.removeListener(_onUserChanged);

    // 移除活动数据变化监听
    Services.fishingActivity.removeListener(_onActivitiesDataChanged);

    // 清理标记缓存
    _spotMarkerCache.clear();
    _activityMarkerCache.clear();

    super.dispose();
  }

  /// 处理活动数据变化
  void _onActivitiesDataChanged() {
    debugPrint('📢 [地图页面] 收到活动数据变化通知');
    if (mounted) {
      // 使用响应式更新而不是强制刷新
      refreshActivities();
    }
  }

  /// 公共方法：触发添加钓点模式（供MainScreen调用）
  void triggerAddSpotMode() {
    _isAddingActivity = false;
    _toggleSplitScreenModeWithLocationName();
  }

  /// 公共方法：触发添加活动模式
  void triggerAddActivityMode() {
    _isAddingActivity = true;
    _toggleSplitScreenModeWithLocationName();
  }

  /// 公共方法：导航到指定钓点（已废弃，请使用统一导航服务）
  @deprecated
  void navigateToSpot(dynamic spotLocation, String spotId) {
    try {
      debugPrint('🗺️ [MapPage] ========== 开始导航到钓点 ==========');
      debugPrint('🗺️ [MapPage] 目标钓点ID: $spotId');
      debugPrint('🗺️ [MapPage] 接收到的位置信息: $spotLocation');
      debugPrint('🗺️ [MapPage] 位置信息类型: ${spotLocation.runtimeType}');
      debugPrint('🗺️ [MapPage] 组件mounted状态: $mounted');
      debugPrint('🗺️ [MapPage] 地图准备状态: $_isMapReady');

      // 解析位置信息
      LatLng targetLocation;
      if (spotLocation is LatLng) {
        targetLocation = spotLocation;
        debugPrint('🗺️ [MapPage] 位置信息类型: LatLng对象');
      } else if (spotLocation is Map<String, dynamic>) {
        debugPrint('🗺️ [MapPage] 位置信息类型: Map对象');
        final lat = spotLocation['latitude'] as double?;
        final lng = spotLocation['longitude'] as double?;
        debugPrint('🗺️ [MapPage] 解析纬度: $lat');
        debugPrint('🗺️ [MapPage] 解析经度: $lng');

        if (lat != null && lng != null) {
          targetLocation = LatLng(lat, lng);
          debugPrint('🗺️ [MapPage] Map格式位置解析成功');
        } else {
          debugPrint('❌ [MapPage] 无效的位置信息格式: $spotLocation');
          return;
        }
      } else {
        debugPrint('❌ [MapPage] 不支持的位置信息类型: ${spotLocation.runtimeType}');
        return;
      }

      debugPrint(
        '🗺️ [MapPage] 解析后的目标位置: ${targetLocation.latitude}, ${targetLocation.longitude}',
      );

      // 检查地图是否已经准备就绪
      if (_isMapReady) {
        debugPrint('🗺️ [MapPage] 地图已准备就绪，立即执行导航');
        _executeNavigation(targetLocation, spotId);
      } else {
        debugPrint('🗺️ [MapPage] 地图未准备就绪，保存导航请求等待地图准备完成');
        _pendingSpotLocation = targetLocation;
        _pendingSpotId = spotId;

        // 设置超时机制，如果地图长时间未准备就绪，强制执行导航
        Future.delayed(const Duration(seconds: 3), () {
          if (!_isMapReady &&
              _pendingSpotLocation != null &&
              _pendingSpotId != null) {
            debugPrint('🗺️ [MapPage] 地图准备超时，强制执行导航');
            _executeNavigation(_pendingSpotLocation!, _pendingSpotId!);
            _pendingSpotLocation = null;
            _pendingSpotId = null;
          }
        });
      }

      debugPrint('🗺️ [MapPage] ========== 地图导航请求处理完成 ==========');
    } catch (e) {
      debugPrint('❌ [MapPage] 导航到钓点失败: $e');
      debugPrint('❌ [MapPage] 错误堆栈: ${StackTrace.current}');
    }
  }

  /// 执行实际的地图导航操作
  void _executeNavigation(LatLng targetLocation, String spotId) {
    try {
      debugPrint('🗺️ [MapPage] ========== 执行地图导航 ==========');
      debugPrint(
        '🗺️ [MapPage] 目标位置: ${targetLocation.latitude}, ${targetLocation.longitude}',
      );
      debugPrint('🗺️ [MapPage] 目标钓点ID: $spotId');

      // 检查MapController状态
      try {
        debugPrint('🗺️ [MapPage] MapController状态: $mapController');
        debugPrint('🗺️ [MapPage] 当前地图中心: ${mapController.camera.center}');
        debugPrint('🗺️ [MapPage] 当前缩放级别: ${mapController.camera.zoom}');
      } catch (e) {
        debugPrint('❌ [MapPage] 无法获取MapController状态: $e');
        // 继续尝试执行导航
      }

      debugPrint('🗺️ [MapPage] 准备移动地图到目标位置');

      // 移动地图到目标位置，使用较高的缩放级别以便清楚看到钓点
      mapController.move(targetLocation, 16.0);
      debugPrint('🗺️ [MapPage] 地图移动命令已执行');

      // 延迟加载钓点，确保地图移动完成
      debugPrint('🗺️ [MapPage] 设置延迟加载钓点 (800ms)');
      Future.delayed(const Duration(milliseconds: 800), () {
        debugPrint('🗺️ [MapPage] 延迟时间到，检查mounted状态: $mounted');
        if (mounted) {
          debugPrint('🗺️ [MapPage] 开始加载钓点数据');
          _loadSpotsInBounds();

          // 再次延迟，确保钓点加载完成后高亮显示目标钓点
          debugPrint('🗺️ [MapPage] 设置延迟高亮显示 (500ms)');
          Future.delayed(const Duration(milliseconds: 500), () {
            debugPrint('🗺️ [MapPage] 高亮延迟时间到，检查mounted状态: $mounted');
            if (mounted) {
              debugPrint('🗺️ [MapPage] 开始高亮显示目标钓点');
              _highlightTargetSpot(spotId);
            } else {
              debugPrint('❌ [MapPage] 组件已销毁，跳过高亮显示');
            }
          });
        } else {
          debugPrint('❌ [MapPage] 组件已销毁，跳过钓点加载');
        }
      });

      debugPrint('🗺️ [MapPage] ========== 地图导航执行完成 ==========');
    } catch (e) {
      debugPrint('❌ [MapPage] 执行地图导航失败: $e');
      debugPrint('❌ [MapPage] 错误堆栈: ${StackTrace.current}');
    }
  }

  /// 高亮显示目标钓点
  void _highlightTargetSpot(String spotId) {
    try {
      debugPrint('🗺️ [MapPage] ========== 开始高亮显示钓点 ==========');
      debugPrint('🗺️ [MapPage] 目标钓点ID: $spotId');
      debugPrint('🗺️ [MapPage] 当前加载的钓点数量: ${_spots.length}');
      debugPrint('🗺️ [MapPage] 已加载的钓点列表:');

      for (int i = 0; i < _spots.length; i++) {
        final spot = _spots[i];
        debugPrint(
          '🗺️ [MapPage]   [$i] ID: ${spot.id}, 名称: ${spot.name}, 位置: ${spot.locationLatLng}',
        );
      }

      // 查找目标钓点
      final targetSpot = _spots.firstWhere(
        (spot) => spot.id == spotId,
        orElse: () => throw Exception('钓点未找到'),
      );

      debugPrint('🗺️ [MapPage] ✅ 找到目标钓点: ${targetSpot.name}');
      debugPrint('🗺️ [MapPage] 目标钓点位置: ${targetSpot.locationLatLng}');
      debugPrint('🗺️ [MapPage] 当前地图中心: ${mapController.camera.center}');
      debugPrint('🗺️ [MapPage] 当前缩放级别: ${mapController.camera.zoom}');

      // 这里可以添加高亮显示逻辑，比如：
      // 1. 显示一个临时的高亮标记
      // 2. 播放一个简短的动画
      // 3. 显示钓点信息弹窗

      // 暂时通过日志输出来确认功能正常
      debugPrint('🗺️ [MapPage] ✅ 钓点 "${targetSpot.name}" 已定位并高亮显示');
      debugPrint('🗺️ [MapPage] ========== 钓点高亮显示完成 ==========');
    } catch (e) {
      debugPrint('❌ [MapPage] 高亮显示钓点失败: $e');
      debugPrint('❌ [MapPage] 可能原因: 钓点未在当前视图范围内加载');
      debugPrint('❌ [MapPage] 当前地图边界信息:');

      final bounds = mapController.camera.visibleBounds;
      debugPrint('❌ [MapPage] 北边界: ${bounds.north}');
      debugPrint('❌ [MapPage] 南边界: ${bounds.south}');
      debugPrint('❌ [MapPage] 东边界: ${bounds.east}');
      debugPrint('❌ [MapPage] 西边界: ${bounds.west}');

      // 即使高亮失败，地图导航仍然成功
    }
  }

  /// 公共属性：获取分屏模式状态（供MainScreen访问）
  bool get isSplitScreenMode => _isSplitScreenMode;

  /// 处理统一导航请求 ⭐ 新增
  void _handleUnifiedNavigation(MapNavigationParams params) {
    debugPrint('🗺️ [MapPage] 收到统一导航请求');
    debugPrint('🗺️ [MapPage] 目标位置: ${params.targetLocation}');
    debugPrint('🗺️ [MapPage] 显示临时标记: ${params.showTemporaryMarker}');

    if (_isMapReady) {
      _executeUnifiedNavigation(params);
    } else {
      debugPrint('🗺️ [MapPage] 地图未准备就绪，保存导航请求');
      _pendingNavigationParams = params;
    }
  }

  /// 执行统一导航 ⭐ 新增
  void _executeUnifiedNavigation(MapNavigationParams params) {
    try {
      debugPrint('🗺️ [MapPage] 开始执行统一导航');

      // 处理临时标记
      setState(() {
        if (params.showTemporaryMarker) {
          _temporaryMarkerLocation = params.targetLocation;
          _showTemporaryMarker = true;
          debugPrint('🗺️ [MapPage] 显示蓝色临时标记于: $_temporaryMarkerLocation');
        } else {
          _showTemporaryMarker = false;
          debugPrint('🗺️ [MapPage] 隐藏临时标记');
        }
      });

      // 移动地图到目标位置，使用缩放级别16
      mapController.move(params.targetLocation, 16.0);
      debugPrint('🗺️ [MapPage] 地图移动到: ${params.targetLocation}，缩放级别: 16');

      // 延迟加载钓点
      Future.delayed(const Duration(milliseconds: 800), () {
        if (mounted) {
          _loadSpotsInBounds();
          debugPrint('🗺️ [MapPage] 开始加载钓点数据');
        }
      });
    } catch (e) {
      debugPrint('❌ [MapPage] 执行统一导航失败: $e');
    }
  }

  /// 构建临时标记 ⭐ 新增
  Widget _buildTemporaryMarker() {
    return FaIcon(
      FontAwesomeIcons.locationDot,
      size: 30,
      color: const Color.fromARGB(201, 255, 213, 2), // 蓝色临时标记
    );
  }

  /// 公共方法：供外部调用的统一导航接口 ⭐ 新增
  void executeNavigation(MapNavigationParams params) {
    _handleUnifiedNavigation(params);
  }

  // 初始化数据
  Future<void> _initializeData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 静默尝试登录（不显示任何错误提示）
      try {
        if (!Services.auth.isLoggedIn) {
          await Services.auth.initialize();
        }
      } catch (e) {
        // 静默处理登录失败，不显示任何提示
        debugPrint('静默登录失败: $e');
      }

      // 初始化瓦片缓存服务
      await Services.cache.initialize();

      // 获取本地存储的位置或默认位置
      final initialLocation = Services.location.getCurrentLocation();

      setState(() {
        _userLocation = initialLocation;
        _isLoading = false;
      });

      // 移动地图到用户位置（使用最大缩放级别以便用户看到详细内容）
      mapController.move(_userLocation, 18.0);

      // 延迟加载钓点，确保地图移动完成
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _loadSpotsInBounds();
        }
      });

      // 异步获取最新位置（不阻塞UI）
      Services.location
          .requestLocationUpdate()
          .then((newLocation) {
            if (mounted) {
              setState(() {
                _userLocation = newLocation;
              });

              // 只有当位置变化较大时才移动地图（超过100米）
              final distance = Services.location.calculateDistance(
                _userLocation,
                newLocation,
              );
              if (distance > 0.1) {
                // 0.1公里 = 100米
                mapController.move(newLocation, mapController.camera.zoom);
                _loadSpotsInBounds(); // 重新加载新位置范围内的钓点
              }
            }
          })
          .catchError((e) {
            debugPrint('异步获取位置失败: $e');
          });

      // 启动定时位置更新
      _startPeriodicLocationUpdate();

      // 启动位置监听
      _startLocationListening();
    } catch (e) {
      debugPrint('初始化数据失败: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 启动定时位置更新
  void _startPeriodicLocationUpdate() {
    if (!_enablePeriodicLocationUpdate) return;

    _locationUpdateTimer = Timer.periodic(_locationUpdateInterval, (
      timer,
    ) async {
      if (!mounted) {
        timer.cancel();
        return;
      }

      try {
        final newLocation = await Services.location.requestLocationUpdate();
        if (mounted) {
          final distance = Services.location.calculateDistance(
            _userLocation,
            newLocation,
          );

          // 只有当位置变化超过50米时才更新UI
          if (distance > 0.05) {
            setState(() {
              _userLocation = newLocation;
            });

            // 如果位置变化较大（超过500米），重新加载钓点
            if (distance > 0.5) {
              _loadSpotsInBounds();
            }
          }
        }
      } catch (e) {
        debugPrint('定时位置更新失败: $e');
      }
    });
  }

  // 启动位置监听
  void _startLocationListening() {
    // 启动位置服务的实时监听
    Services.location.startLocationTracking();

    // 监听位置变化流
    _locationSubscription = Services.location.locationStream.listen(
      (LatLng newLocation) {
        if (mounted) {
          final distance = Services.location.calculateDistance(
            _userLocation,
            newLocation,
          );

          // 只有当位置变化超过20米时才更新UI
          if (distance > 0.02) {
            setState(() {
              _userLocation = newLocation;
            });

            // 如果位置变化较大（超过200米），重新加载钓点
            if (distance > 0.2) {
              _loadSpotsInBounds();
            }
          }
        }
      },
      onError: (error) {
        debugPrint('位置监听错误: $error');
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用，用于AutomaticKeepAliveClientMixin

    debugPrint('🔍 [地图构建] 当前钓点数量: ${_spots.length}');
    debugPrint('🔍 [地图构建] 当前活动数量: ${_activities.length}');
    if (_activities.isNotEmpty) {
      debugPrint('🔍 [地图构建] 活动列表:');
      for (final activity in _activities) {
        debugPrint(
          '  - ${activity.id}: ${activity.title} (${activity.activityType})',
        );
      }
    }

    return PopScope(
      canPop: !_isSplitScreenMode, // 分屏模式下阻止默认返回行为
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop && _isSplitScreenMode) {
          // 在分屏模式下按返回键时，退出分屏模式
          _toggleSplitScreenMode();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          actions: const [
            DevMenu(), // 开发者菜单（仅在开发模式下显示）
          ],
        ),
        extendBodyBehindAppBar: true, // 让body延伸到AppBar后面
        body:
            _isSplitScreenMode
                ? _buildSplitScreenLayout()
                : _buildNormalLayout(),
        // [*参数调整*]右下角地图控制按钮（分屏模式下或搜索时隐藏）
        floatingActionButton:
            _isSplitScreenMode || _isSearching
                ? null
                : Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    SizedBox(
                      width: 50, // 缩小按钮宽度
                      height: 50, // 缩小按钮高度
                      child: FloatingActionButton(
                        onPressed: () {
                          // 切换地图类型
                          setState(() {
                            isVectorMap = !isVectorMap;
                          });
                        },
                        heroTag: 'switchMap',
                        tooltip: '切换地图类型',
                        backgroundColor: Colors.white,
                        child: FaIcon(
                          isVectorMap
                              ? FontAwesomeIcons.map
                              : FontAwesomeIcons.satellite,
                          size: 20, // 缩小图标尺寸
                        ),
                      ),
                    ),
                    const SizedBox(height: 12), // 缩小间距
                    SizedBox(
                      width: 50, // 缩小按钮宽度
                      height: 50, // 缩小按钮高度
                      child: FloatingActionButton(
                        onPressed: () {
                          // 切换注记层显示
                          setState(() {
                            showAnnotationLayer = !showAnnotationLayer;
                          });
                        },
                        heroTag: 'toggleAnnotation',
                        tooltip: '切换注记层',
                        backgroundColor: Colors.white,
                        child: FaIcon(
                          showAnnotationLayer
                              ? FontAwesomeIcons.layerGroup
                              : FontAwesomeIcons.square,
                          size: 20, // 缩小图标尺寸
                        ),
                      ),
                    ),
                    const SizedBox(height: 12), // 缩小间距
                    SizedBox(
                      width: 50, // 缩小按钮宽度
                      height: 50, // 缩小按钮高度
                      child: FloatingActionButton(
                        onPressed:
                            _isLocationResetting
                                ? null
                                : () {
                                  // 重置地图位置并获取最新位置
                                  _updateCurrentLocation();
                                },
                        heroTag: 'resetLocation',
                        tooltip: '重置位置',
                        backgroundColor:
                            _isLocationResetting ? Colors.grey : Colors.white,
                        child:
                            _isLocationResetting
                                ? const SizedBox(
                                  width: 16, // 缩小加载指示器
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Colors.white,
                                  ),
                                )
                                : const FaIcon(
                                  FontAwesomeIcons.locationCrosshairs,
                                  size: 20,
                                ), // 缩小图标尺寸
                      ),
                    ),
                  ],
                ),
      ),
    );
  }

  /// 处理搜索位置选择 ⭐ 新增
  void _handleSearchLocationSelected(LatLng location) {
    // 使用统一导航服务处理搜索位置选择
    Services.mapNavigation.navigateToSearchLocation(
      location: location,
      context: context,
    );
    debugPrint('通过统一导航服务处理搜索位置: ${location.latitude}, ${location.longitude}');
  }

  // 构建正常布局
  Widget _buildNormalLayout() {
    return Stack(
      children: [
        _isLoading
            ? const Center(child: CircularProgressIndicator())
            : FlutterMap(
              key: _mapKey,
              mapController: mapController,
              options: MapOptions(
                initialCenter: _userLocation,
                initialZoom: 18.0, // 设置为最大缩放级别
                minZoom: 1, // 设置最小缩放级别
                maxZoom: 18.0, // 设置最大缩放级别
                // 禁用旋转功能
                keepAlive: true,
                // 地图准备就绪时加载钓点
                onMapReady: () {
                  debugPrint('🗺️ [MapPage] 地图准备就绪 (正常布局)');
                  _isMapReady = true;

                  Future.delayed(const Duration(milliseconds: 100), () {
                    if (mounted) {
                      _loadSpotsInBounds();

                      // 检查是否有待执行的统一导航请求 ⭐ 新增
                      if (_pendingNavigationParams != null) {
                        debugPrint('🗺️ [MapPage] 执行待处理的统一导航请求');
                        _executeUnifiedNavigation(_pendingNavigationParams!);
                        _pendingNavigationParams = null;
                      }

                      // 检查是否有待执行的导航请求（兼容旧版本）
                      if (_pendingSpotLocation != null &&
                          _pendingSpotId != null) {
                        debugPrint('🗺️ [MapPage] 执行待处理的导航请求');
                        _executeNavigation(
                          _pendingSpotLocation!,
                          _pendingSpotId!,
                        );
                        _pendingSpotLocation = null;
                        _pendingSpotId = null;
                      }
                    }
                  });
                },
                // 地图点击事件 - 退出搜索状态
                onTap: (tapPosition, point) {
                  _handleMapTap();
                },
              ),
              children: [
                // 天地图瓦片层
                TileLayer(
                  urlTemplate: TianDiTuUtils.buildTileUrlTemplate(
                    isVector: isVectorMap,
                    isAnnotation: false,
                  ),
                  additionalOptions: {'k': TianDiTuUtils.key},
                  subdomains: TianDiTuUtils.subdomains,
                  // 使用带缓存的瓦片提供者提高性能
                  tileProvider: Services.cache.createCachedTileProvider(),
                ),
                // 注记层
                if (showAnnotationLayer)
                  TileLayer(
                    urlTemplate: TianDiTuUtils.buildTileUrlTemplate(
                      isVector: isVectorMap,
                      isAnnotation: true,
                    ),
                    additionalOptions: {'k': TianDiTuUtils.key},
                    subdomains: TianDiTuUtils.subdomains,
                    // 使用带缓存的瓦片提供者提高性能
                    tileProvider: Services.cache.createCachedTileProvider(),
                  ),
                // 位置标记层 - 可调整大小和对齐
                CurrentLocationLayer(
                  style: LocationMarkerStyle(
                    marker: DefaultLocationMarker(
                      child: Container(
                        width: 40,
                        height: 40,
                        alignment: Alignment.center, // 确保箭头居中
                        child: const Icon(
                          Icons.navigation,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),
                    markerSize: const Size(35, 35), // [*参数调整*]调整位置标记大小
                    markerDirection: MarkerDirection.heading,
                  ),
                ),
                // 添加钓点标记
                MarkerLayer(
                  key: ValueKey('markers_v$_activitiesVersion'), // 使用数据版本号作为key
                  markers: [
                    // 钓点标记
                    for (final spot in _spots)
                      Marker(
                        point: spot.locationLatLng,
                        width: AppConfig.instance.mapMarkerSize, // 扩大以适应新的点击区域
                        height: AppConfig.instance.mapMarkerSize,
                        // 确保标记点始终保持竖直
                        rotate: true,
                        alignment:
                            Alignment
                                .topCenter, // [*调整参数*]地图上位置标记尖端在widget中心，使用默认居中对齐
                        child: _buildCachedSpotMarker(
                          spot,
                          size: AppConfig.instance.mapMarkerSize,
                        ),
                      ),
                    // 活动标记 ⭐ 新增
                    for (final activity in _activities)
                      if (activity.location != null)
                        Marker(
                          point: LatLng(
                            activity.location!['lat'] as double,
                            activity.location!['lon'] as double,
                          ),
                          width: AppConfig.instance.mapMarkerSize,
                          height: AppConfig.instance.mapMarkerSize,
                          rotate: true,
                          alignment: Alignment.topCenter,
                          child: _buildCachedActivityMarker(
                            activity,
                            size: AppConfig.instance.mapMarkerSize,
                          ),
                        ),
                  ],
                ),

                // 添加临时标记层 ⭐ 新增
                if (_showTemporaryMarker && _temporaryMarkerLocation != null)
                  MarkerLayer(
                    markers: [
                      Marker(
                        point: _temporaryMarkerLocation!,
                        width: 30,
                        height: 40,
                        alignment: Alignment.topCenter, // 与钓点标记保持一致的对齐方式
                        rotate: true, // 随地图旋转而旋转
                        child: _buildTemporaryMarker(),
                      ),
                    ],
                  ),
              ],
            ),

        // 优化后的搜索栏
        OptimizedSearchBar(
          key: _searchBarKey,
          currentLocation: _userLocation,
          onLocationSelected: _handleSearchLocationSelected, // ⭐ 修改为使用统一导航服务
          hintText: '搜索钓点、地址',
          onSearchStarted: () {
            setState(() {
              _isSearching = true;
            });
            // 搜索时隐藏底部导航栏
            widget.onBottomNavigationBarVisibilityChanged?.call(false);
          },
          onSearchEnded: () {
            setState(() {
              _isSearching = false;
            });
            // 搜索结束时显示底部导航栏
            widget.onBottomNavigationBarVisibilityChanged?.call(true);
          },
        ),
      ],
    );
  }

  // 构建分屏布局
  Widget _buildSplitScreenLayout() {
    return Stack(
      children: [
        // 全屏地图作为背景
        FlutterMap(
          key: _mapKey,
          mapController: mapController,
          options: MapOptions(
            initialCenter: _userLocation,
            initialZoom: 18.0, // 设置为最大缩放级别
            minZoom: 1,
            maxZoom: 18.0,
            keepAlive: true,
            // 地图准备就绪时加载钓点
            onMapReady: () {
              debugPrint('🗺️ [MapPage] 地图准备就绪 (分屏布局)');
              _isMapReady = true;

              Future.delayed(const Duration(milliseconds: 100), () {
                if (mounted) {
                  _loadSpotsInBounds();

                  // 检查是否有待执行的统一导航请求 ⭐ 新增
                  if (_pendingNavigationParams != null) {
                    debugPrint('🗺️ [MapPage] 执行待处理的统一导航请求');
                    _executeUnifiedNavigation(_pendingNavigationParams!);
                    _pendingNavigationParams = null;
                  }

                  // 检查是否有待执行的导航请求（兼容旧版本）
                  if (_pendingSpotLocation != null && _pendingSpotId != null) {
                    debugPrint('🗺️ [MapPage] 执行待处理的导航请求');
                    _executeNavigation(_pendingSpotLocation!, _pendingSpotId!);
                    _pendingSpotLocation = null;
                    _pendingSpotId = null;
                  }
                }
              });
            },
            // 地图移动时更新中心标记位置 - 基于屏幕25%高度处
            onPositionChanged: (position, hasGesture) {
              if (hasGesture) {
                setState(() {
                  // 计算屏幕25%高度处对应的地图坐标
                  _centerMarkerLocation = _calculateLocationAt25PercentHeight();
                });
              }
            },
            // 地图点击事件 - 退出搜索状态
            onTap: (tapPosition, point) {
              _handleMapTap();
            },
          ),
          children: [
            // 底图层
            TileLayer(
              urlTemplate: TianDiTuUtils.buildTileUrlTemplate(
                isVector: isVectorMap,
                isAnnotation: false,
              ),
              additionalOptions: {'k': TianDiTuUtils.key},
              subdomains: TianDiTuUtils.subdomains,
              userAgentPackageName: 'com.example.fishing_app',
            ),

            // 注记层
            if (showAnnotationLayer)
              TileLayer(
                urlTemplate: TianDiTuUtils.buildTileUrlTemplate(
                  isVector: isVectorMap,
                  isAnnotation: true,
                ),
                additionalOptions: {'k': TianDiTuUtils.key},
                subdomains: TianDiTuUtils.subdomains,
                userAgentPackageName: 'com.example.fishing_app',
              ),

            // 添加钓点标记
            // 位置标记层 - 可调整大小和对齐
            CurrentLocationLayer(
              style: LocationMarkerStyle(
                marker: DefaultLocationMarker(
                  child: Container(
                    width: 40,
                    height: 40,
                    alignment: Alignment.center, // 确保箭头居中
                    child: const Icon(
                      Icons.navigation,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
                markerSize: const Size(35, 35), // [*参数调整*]调整位置标记大小
                markerDirection: MarkerDirection.heading,
              ),
            ),
            MarkerLayer(
              key: ValueKey(
                'split_markers_v$_activitiesVersion',
              ), // 使用数据版本号作为key
              markers: [
                // 钓点标记
                for (final spot in _spots)
                  Marker(
                    point: spot.locationLatLng,
                    width: AppConfig.instance.mapMarkerSize, // 扩大以适应新的点击区域
                    height: AppConfig.instance.mapMarkerSize,
                    rotate: true,
                    alignment: Alignment.topCenter, // 尖端在widget中心，使用默认居中对齐
                    child: _buildCachedSpotMarker(
                      spot,
                      size: AppConfig.instance.mapMarkerSize,
                    ),
                  ),
                // 活动标记 ⭐ 使用新的ActivityMarker组件
                for (final activity in _activities)
                  if (activity.location != null)
                    Marker(
                      point: activity.locationLatLng,
                      width: AppConfig.instance.mapMarkerSize,
                      height: AppConfig.instance.mapMarkerSize,
                      rotate: true,
                      alignment: Alignment.topCenter, // 与钓点标记保持一致
                      child: _buildCachedActivityMarker(
                        activity,
                        size: AppConfig.instance.mapMarkerSize,
                      ),
                    ),
              ],
            ),

            // 添加临时标记层 ⭐ 新增
            if (_showTemporaryMarker && _temporaryMarkerLocation != null)
              MarkerLayer(
                markers: [
                  Marker(
                    point: _temporaryMarkerLocation!,
                    width: 30,
                    height: 40,
                    alignment: Alignment.topCenter, // 与钓点标记保持一致的对齐方式
                    rotate: true, // 随地图旋转而旋转
                    child: _buildTemporaryMarker(),
                  ),
                ],
              ),
          ],
        ),

        // 中心标记覆盖层 - 位于屏幕顶部往下25%处
        Positioned(
          left: 0,
          right: 0,
          top: MarkerAlignmentUtils.calculatePinOffset(
            screenHeight: MediaQuery.of(context).size.height,
            targetHeightPercent: 0.25,
            pinSize: 40.0,
          ), // 精确计算图钉偏移，使针尖指向坐标点
          child: const Text(
            '📍',
            style: TextStyle(fontSize: 40),
            textAlign: TextAlign.center,
          ),
        ),

        // 底部表单 - 根据模式显示不同组件，带滑入动画
        AnimatedBuilder(
          animation: _splitScreenAnimation,
          builder: (context, child) {
            final offset =
                (1 - _splitScreenAnimation.value) *
                MediaQuery.of(context).size.height *
                0.75;
            return Transform.translate(offset: Offset(0, offset), child: child);
          },
          child:
              _isAddingActivity
                  ? SplitScreenAddActivity(
                    location: _centerMarkerLocation,
                    suggestedName: _suggestedSpotName,
                    onLocationChanged: (newLocation) {
                      setState(() {
                        _centerMarkerLocation = newLocation;
                      });
                    },
                    onClose: () {
                      _toggleSplitScreenMode();
                    },
                    onSpotAdded: (activity) {
                      setState(() {
                        _activities.add(activity);
                        // 添加到已加载的活动集合中
                        _loadedActivityIds.add(activity.id);
                      });

                      // 设置新活动位置作为目标位置，这样退出分屏模式时会移动到这里
                      final activityLocation = activity.locationLatLng;
                      _originalMapCenter = activityLocation;

                      // 关闭分屏模式（会自动移动到_originalMapCenter位置）
                      _toggleSplitScreenMode();
                    },
                  )
                  : SplitScreenAddSpot(
                    location: _centerMarkerLocation,
                    suggestedName: _suggestedSpotName,
                    onLocationChanged: (newLocation) {
                      setState(() {
                        _centerMarkerLocation = newLocation;
                      });
                    },
                    onClose: () {
                      _toggleSplitScreenMode();
                    },
                    onSpotAdded: (spot) {
                      setState(() {
                        _spots.add(spot);
                        // 添加到已加载的钓点集合中
                        _loadedSpotIds.add(spot.id);
                      });

                      // 设置新钓点位置作为目标位置，这样退出分屏模式时会移动到这里
                      final spotLocation = spot.locationLatLng;
                      _originalMapCenter = spotLocation;

                      // 关闭分屏模式（会自动移动到_originalMapCenter位置）
                      _toggleSplitScreenMode();
                    },
                  ),
        ),
      ],
    );
  }

  // 切换分屏模式
  void _toggleSplitScreenMode() {
    final wasInSplitScreenMode = _isSplitScreenMode;

    setState(() {
      _isSplitScreenMode = !_isSplitScreenMode;

      if (_isSplitScreenMode) {
        // 进入分屏模式时，设置中心标记位置为屏幕25%高度处
        _centerMarkerLocation = _calculateLocationAt25PercentHeight();
        // 启动进入动画
        _splitScreenAnimationController.forward();
      } else {
        // 退出分屏模式时，启动退出动画
        _splitScreenAnimationController.reverse();
        // 恢复原来的地图中心位置
        if (_originalMapCenter != null) {
          mapController.move(_originalMapCenter!, mapController.camera.zoom);
          _originalMapCenter = null; // 清除保存的位置
        }
        // 清除建议的钓点名称
        _suggestedSpotName = null;
      }
    });

    // 使用postFrameCallback确保在正确的时机调用回调
    if (_isSplitScreenMode && !wasInSplitScreenMode) {
      // 刚进入分屏模式，隐藏底部导航栏
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          widget.onBottomNavigationBarVisibilityChanged?.call(false);
        }
      });
    } else if (!_isSplitScreenMode && wasInSplitScreenMode) {
      // 刚退出分屏模式，显示底部导航栏
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          widget.onBottomNavigationBarVisibilityChanged?.call(true);
        }
      });
    }
  }

  // 切换分屏模式并获取位置名称
  Future<void> _toggleSplitScreenModeWithLocationName() async {
    if (_isSplitScreenMode) {
      // 如果已经在分屏模式，直接退出
      _toggleSplitScreenMode();
      return;
    }

    // 检查用户是否已登录
    if (!Services.auth.isLoggedIn) {
      // 显示登录提示对话框
      _showLoginRequiredDialog();
      return;
    }

    // 1. 读取屏幕中心点的经纬度坐标
    final screenCenterCoordinate = mapController.camera.center;

    // 保存当前地图中心位置，用于退出时恢复
    _originalMapCenter = screenCenterCoordinate;

    // 3. 验证坐标转换准确性（调试用）
    final screenSize = MediaQuery.of(context).size;
    MapCoordinateUtils.validateCoordinateConversion(
      mapController.camera,
      screenCenterCoordinate,
      screenSize,
    );

    // 4. 设置分屏模式状态并打开添加钓点页面
    setState(() {
      _isSplitScreenMode = true;
      _centerMarkerLocation = screenCenterCoordinate; // 标记位置就是原来的屏幕中心
    });

    // 启动动画，并添加地图移动监听器
    // 添加动画监听器，实现地图平滑移动
    late VoidCallback animationListener;
    animationListener = () {
      // 计算当前动画进度对应的地图位置
      final progress = _splitScreenAnimation.value;

      // 计算目标位置（屏幕25%高度处对应的地图坐标）
      final targetLocation = _calculateTargetLocationForSplitScreen(
        screenCenterCoordinate,
      );

      // 在原始位置和目标位置之间插值
      final currentLat =
          screenCenterCoordinate.latitude +
          (targetLocation.latitude - screenCenterCoordinate.latitude) *
              progress;
      final currentLng =
          screenCenterCoordinate.longitude +
          (targetLocation.longitude - screenCenterCoordinate.longitude) *
              progress;

      // 移动地图到当前插值位置
      mapController.move(
        LatLng(currentLat, currentLng),
        mapController.camera.zoom,
      );

      // 当动画完成时，移除监听器
      if (progress >= 1.0) {
        _splitScreenAnimation.removeListener(animationListener);
      }
    };

    _splitScreenAnimation.addListener(animationListener);
    _splitScreenAnimationController.forward();

    // 隐藏底部导航栏
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        widget.onBottomNavigationBarVisibilityChanged?.call(false);
      }
    });

    // 异步获取位置名称
    try {
      debugPrint(
        '开始获取位置名称: ${screenCenterCoordinate.latitude}, ${screenCenterCoordinate.longitude}',
      );

      final locationName = await _getTianDiTuLocationName(
        screenCenterCoordinate.longitude,
        screenCenterCoordinate.latitude,
      );

      if (locationName != null && locationName.trim().isNotEmpty) {
        setState(() {
          _suggestedSpotName = '${locationName.trim()}钓点';
        });
        debugPrint('获取到位置名称: $_suggestedSpotName');
      } else {
        debugPrint('未获取到有效的位置名称');
        setState(() {
          _suggestedSpotName = null;
        });
      }
    } catch (e) {
      debugPrint('获取位置名称失败: $e');
      setState(() {
        _suggestedSpotName = null;
      });
    }
  }

  // 直接调用天地图逆地理编码API
  Future<String?> _getTianDiTuLocationName(
    double longitude,
    double latitude,
  ) async {
    try {
      // 使用真实的天地图API调用
      debugPrint('开始调用天地图API获取位置名称: 经度=$longitude, 纬度=$latitude');

      final locationName = await TianDiTuUtils.getBestLocationName(
        longitude,
        latitude,
      );

      if (locationName != null && locationName.trim().isNotEmpty) {
        debugPrint('天地图API返回位置名称: $locationName');
        return locationName;
      } else {
        debugPrint('天地图API未返回有效位置名称，使用模拟数据');

        // 如果API调用失败，使用模拟数据作为备选
        if (latitude > 39.9 &&
            latitude < 40.0 &&
            longitude > 116.3 &&
            longitude < 116.5) {
          return '天安门广场';
        } else if (latitude > 31.2 &&
            latitude < 31.3 &&
            longitude > 121.4 &&
            longitude < 121.5) {
          return '外滩';
        } else if (latitude > 22.5 &&
            latitude < 22.6 &&
            longitude > 114.0 &&
            longitude < 114.2) {
          return '维多利亚港';
        } else {
          return '未知地点';
        }
      }
    } catch (e) {
      debugPrint('获取位置名称失败: $e，使用模拟数据');

      // 异常时使用模拟数据作为备选
      if (latitude > 39.9 &&
          latitude < 40.0 &&
          longitude > 116.3 &&
          longitude < 116.5) {
        return '天安门广场';
      } else {
        return '未知地点';
      }
    }
  }

  // 用于防止频繁加载的变量
  bool _isLoadingSpots = false;
  DateTime _lastLoadTime = DateTime.now();

  // 已加载的区域范围（用于增量加载）
  double? _loadedMinLat;
  double? _loadedMaxLat;
  double? _loadedMinLng;
  double? _loadedMaxLng;

  // 已加载的钓点集合（避免重复）
  final Set<String> _loadedSpotIds = {};
  // 已加载的活动集合（避免重复）
  final Set<String> _loadedActivityIds = {};

  // 根据地图范围加载钓点（增量加载）
  Future<void> _loadSpotsInBounds() async {
    // 如果页面正在加载或者已经在加载钓点，则跳过
    if (_isLoading || _isLoadingSpots) return;

    // 防止频繁加载：如果距离上次加载不足500毫秒，则跳过
    final now = DateTime.now();
    if (now.difference(_lastLoadTime).inMilliseconds < 500) return;

    _isLoadingSpots = true;
    _lastLoadTime = now;

    final bounds = mapController.camera.visibleBounds;

    // 计算2倍范围的边界
    final latRange = bounds.north - bounds.south;
    final lngRange = bounds.east - bounds.west;

    final expandedMinLat = bounds.south - latRange;
    final expandedMaxLat = bounds.north + latRange;
    final expandedMinLng = bounds.west - lngRange;
    final expandedMaxLng = bounds.east + lngRange;

    try {
      List<List<double>> newRegions = [];

      if (_loadedMinLat == null) {
        // 首次加载：加载2倍范围的所有钓点
        newRegions.add([
          expandedMinLat,
          expandedMaxLat,
          expandedMinLng,
          expandedMaxLng,
        ]);

        // 更新已加载范围
        _loadedMinLat = expandedMinLat;
        _loadedMaxLat = expandedMaxLat;
        _loadedMinLng = expandedMinLng;
        _loadedMaxLng = expandedMaxLng;
      } else {
        // 增量加载：只加载新增区域
        newRegions = _calculateNewRegions(
          expandedMinLat,
          expandedMaxLat,
          expandedMinLng,
          expandedMaxLng,
          _loadedMinLat!,
          _loadedMaxLat!,
          _loadedMinLng!,
          _loadedMaxLng!,
        );

        if (newRegions.isNotEmpty) {
          // 更新已加载范围为并集
          _loadedMinLat = math.min(_loadedMinLat!, expandedMinLat);
          _loadedMaxLat = math.max(_loadedMaxLat!, expandedMaxLat);
          _loadedMinLng = math.min(_loadedMinLng!, expandedMinLng);
          _loadedMaxLng = math.max(_loadedMaxLng!, expandedMaxLng);
        } else {
          _isLoadingSpots = false;
          return;
        }
      }

      // 加载新区域的钓点和活动
      final List<FishingSpot> newSpots = [];
      final List<FishingActivity> newActivities = [];
      for (final region in newRegions) {
        // 计算区域中心点和半径
        final centerLat = (region[0] + region[1]) / 2;
        final centerLng = (region[2] + region[3]) / 2;
        final center = LatLng(centerLat, centerLng);

        // 计算半径（取较大的维度）
        const double kmPerDegree = 111.0;
        final latRange = (region[1] - region[0]) * kmPerDegree;
        final lngRange =
            (region[3] - region[2]) *
            kmPerDegree *
            math.cos(centerLat * math.pi / 180);
        final radiusKm = math.max(latRange, lngRange) / 2;

        // 并行加载钓点和活动
        debugPrint('🔍 [地图加载] 开始加载区域数据: center=$center, radius=${radiusKm}km');
        final futures = await Future.wait([
          Services.fishingSpot.getVisibleSpots(
            center: center,
            radiusKm: radiusKm,
            perPage: 100,
          ),
          Services.fishingActivity.getActivitiesInRegion(
            center: center,
            radiusKm: radiusKm,
          ),
        ]);
        debugPrint('🔍 [地图加载] 区域数据加载完成');

        final regionSpots = futures[0] as List<FishingSpot>;
        final regionActivities = futures[1] as List<FishingActivity>;

        debugPrint(
          '🔍 [地图加载] 获取到 ${regionSpots.length} 个钓点，${regionActivities.length} 个活动',
        );

        // 过滤掉已加载的钓点
        for (final spot in regionSpots) {
          if (!_loadedSpotIds.contains(spot.id)) {
            newSpots.add(spot);
            _loadedSpotIds.add(spot.id);
          }
        }

        debugPrint('🔍 [地图加载] 开始处理 ${regionActivities.length} 个区域活动');

        // 过滤掉已加载的活动，同时检查活动状态
        for (final activity in regionActivities) {
          debugPrint(
            '🔍 [地图加载] 检查活动: ${activity.id} - ${activity.title} (状态: ${activity.status}, 过期: ${activity.isExpired})',
          );

          if (!_loadedActivityIds.contains(activity.id)) {
            // 只添加活跃状态的活动
            if (activity.status == 'active' && !activity.isExpired) {
              newActivities.add(activity);
              _loadedActivityIds.add(activity.id);
              debugPrint('✅ [地图加载] 新增活动: ${activity.id} - ${activity.title}');
            } else {
              debugPrint(
                '❌ [地图加载] 跳过非活跃活动: ${activity.id} - ${activity.title} (状态: ${activity.status}, 过期: ${activity.isExpired})',
              );
            }
          } else {
            debugPrint('⏭️ [地图加载] 跳过已加载活动: ${activity.id} - ${activity.title}');
          }
        }

        debugPrint('🔍 [地图加载] 清理前活动数量: ${_activities.length}');

        // 清理已取消或过期的活动
        final removedActivities = <FishingActivity>[];
        _activities.removeWhere((activity) {
          final shouldRemove =
              activity.status != 'active' || activity.isExpired;
          if (shouldRemove) {
            removedActivities.add(activity);
          }
          return shouldRemove;
        });

        for (final removed in removedActivities) {
          debugPrint(
            '🗑️ [地图加载] 移除非活跃活动: ${removed.id} - ${removed.title} (状态: ${removed.status})',
          );
        }

        // 同时清理对应的缓存和ID记录
        final activeActivityIds = _activities.map((a) => a.id).toSet();
        final removedIds = <String>[];
        _loadedActivityIds.retainWhere((id) {
          final shouldKeep = activeActivityIds.contains(id);
          if (!shouldKeep) {
            removedIds.add(id);
          }
          return shouldKeep;
        });

        for (final removedId in removedIds) {
          debugPrint('🗑️ [地图加载] 清理已加载ID: $removedId');
        }

        // 清理活动标记缓存
        final removedMarkers = <String>[];
        _activityMarkerCache.removeWhere((key, value) {
          final activityId = key.replaceFirst('activity_', '');
          final shouldRemove = !activeActivityIds.contains(activityId);
          if (shouldRemove) {
            removedMarkers.add(key);
          }
          return shouldRemove;
        });

        for (final removedMarker in removedMarkers) {
          debugPrint('🗑️ [地图加载] 清理活动标记缓存: $removedMarker');
        }

        debugPrint('✅ [地图加载] 清理后活动数量: ${_activities.length}');

        debugPrint(
          '🔍 [地图加载] 过滤后新增 ${newSpots.length} 个钓点，${newActivities.length} 个活动',
        );
      }

      if (mounted && (newSpots.isNotEmpty || newActivities.isNotEmpty)) {
        // 使用批量更新避免频繁刷新
        _batchUpdateData(newSpots, newActivities);
      }
    } catch (e) {
      debugPrint('加载范围内钓点失败: $e');
    } finally {
      _isLoadingSpots = false;
    }
  }

  // 计算需要加载的新区域
  List<List<double>> _calculateNewRegions(
    double newMinLat,
    double newMaxLat,
    double newMinLng,
    double newMaxLng,
    double oldMinLat,
    double oldMaxLat,
    double oldMinLng,
    double oldMaxLng,
  ) {
    List<List<double>> regions = [];

    // 检查是否有重叠
    if (newMaxLat < oldMinLat ||
        newMinLat > oldMaxLat ||
        newMaxLng < oldMinLng ||
        newMinLng > oldMaxLng) {
      // 完全不重叠，加载整个新区域
      regions.add([newMinLat, newMaxLat, newMinLng, newMaxLng]);
      return regions;
    }

    // 计算新增的区域（矩形分割）

    // 上方新区域
    if (newMaxLat > oldMaxLat) {
      regions.add([oldMaxLat, newMaxLat, newMinLng, newMaxLng]);
    }

    // 下方新区域
    if (newMinLat < oldMinLat) {
      regions.add([newMinLat, oldMinLat, newMinLng, newMaxLng]);
    }

    // 左侧新区域（排除已包含在上下区域的部分）
    if (newMinLng < oldMinLng) {
      final topLat = math.min(newMaxLat, oldMaxLat);
      final bottomLat = math.max(newMinLat, oldMinLat);
      if (topLat > bottomLat) {
        regions.add([bottomLat, topLat, newMinLng, oldMinLng]);
      }
    }

    // 右侧新区域（排除已包含在上下区域的部分）
    if (newMaxLng > oldMaxLng) {
      final topLat = math.min(newMaxLat, oldMaxLat);
      final bottomLat = math.max(newMinLat, oldMinLat);
      if (topLat > bottomLat) {
        regions.add([bottomLat, topLat, oldMaxLng, newMaxLng]);
      }
    }

    return regions;
  }

  // 重置已加载的区域状态
  void _resetLoadedRegions() {
    _loadedMinLat = null;
    _loadedMaxLat = null;
    _loadedMinLng = null;
    _loadedMaxLng = null;
    _loadedSpotIds.clear();
    _loadedActivityIds.clear();
    _spots.clear();
    _activities.clear();

    // 清理标记缓存
    _spotMarkerCache.clear();
    _activityMarkerCache.clear();
  }

  /// 刷新活动数据 - 产品级实现
  Future<void> refreshActivities() async {
    debugPrint('🔄 [地图页面] 开始刷新活动数据');
    debugPrint('🔄 [地图页面] 刷新前活动数量: ${_activities.length}');

    try {
      // 1. 获取最新的活动数据
      final latestActivities = await Services.fishingActivity.getAllActivities(
        forceRefresh: true,
      );

      // 2. 比较数据变化
      final hasChanges = _hasActivitiesChanged(latestActivities);

      if (hasChanges) {
        debugPrint('🔄 [地图页面] 检测到活动数据变化，更新UI');

        // 3. 更新本地数据
        await _updateActivitiesData(latestActivities);

        // 4. 触发UI更新
        if (mounted) {
          setState(() {
            _activitiesVersion++;
            debugPrint('🔄 [地图页面] 活动数据版本更新: v$_activitiesVersion');
          });
        }
      } else {
        debugPrint('🔄 [地图页面] 活动数据无变化，跳过更新');
      }
    } catch (e) {
      debugPrint('❌ [地图页面] 刷新活动数据失败: $e');
    }
  }

  /// 检查活动数据是否发生变化
  bool _hasActivitiesChanged(List<FishingActivity> newActivities) {
    // 比较活动数量
    if (_activities.length != newActivities.length) {
      return true;
    }

    // 比较活动ID集合
    final currentIds = _activities.map((a) => a.id).toSet();
    final newIds = newActivities.map((a) => a.id).toSet();

    return !currentIds.containsAll(newIds) || !newIds.containsAll(currentIds);
  }

  /// 更新活动数据
  Future<void> _updateActivitiesData(
    List<FishingActivity> newActivities,
  ) async {
    // 清理旧数据
    _loadedActivityIds.clear();
    _activities.clear();
    _activityMarkerCache.clear();

    // 更新为新数据
    _activities.addAll(newActivities);
    _loadedActivityIds.addAll(newActivities.map((a) => a.id));

    debugPrint('🔄 [地图页面] 活动数据更新完成: ${_activities.length} 个活动');
  }

  /// 兼容旧的强制刷新方法
  void forceRefreshActivities() {
    refreshActivities();
  }

  // 用户状态变化回调
  void _onUserChanged() {
    if (!mounted) return;

    // 清理照片标记的全局缓存
    PhotoFishingSpotMarkerBuilder.clearGlobalCache();

    // 重置已加载的钓点数据
    _resetLoadedRegions();

    // 重新加载当前视图范围内的钓点
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        _loadSpotsInBounds();
      }
    });

    // 更新UI
    if (mounted) {
      setState(() {
        // 触发UI重建，显示新用户的钓点数据
      });
    }
  }

  /// 更新当前位置并移动地图 ⭐ 修改为使用统一导航服务
  Future<void> _updateCurrentLocation() async {
    if (_isLocationResetting) return;

    setState(() {
      _isLocationResetting = true;
    });

    try {
      // 异步获取最新位置
      final newLocation = await Services.location.requestLocationUpdate();

      if (mounted) {
        setState(() {
          _isLocationResetting = false;
        });

        // 使用统一导航服务
        await MapNavigationService.instance.navigateToGpsLocation(
          gpsLocation: newLocation,
          context: context,
        );

        // 显示成功提示
        SnackBarService.showSuccess(context, '位置已更新');

        // 重置加载状态并重新加载钓点
        _resetLoadedRegions();
        _loadSpotsInBounds();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLocationResetting = false;
        });

        SnackBarService.showError(context, '获取位置失败: $e');
      }
    }
  }

  /// 确保位置服务已启动
  Future<void> _ensureLocationServiceStarted() async {
    try {
      // 启动位置监听
      await Services.location.startLocationTracking();

      // 请求一次位置更新以确保有初始位置
      await Services.location.requestLocationUpdate();
    } catch (e) {
      debugPrint('位置服务启动失败: $e');
    }
  }

  /// 调试位置服务
  Future<void> _debugLocationServices() async {
    try {
      // 检查位置服务是否启用
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();

      // 检查权限
      LocationPermission permission = await Geolocator.checkPermission();

      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
      }

      // 尝试获取位置
      if (serviceEnabled &&
          permission != LocationPermission.denied &&
          permission != LocationPermission.deniedForever) {
        try {
          await Geolocator.getCurrentPosition(
            locationSettings: const LocationSettings(
              accuracy: LocationAccuracy.medium,
              timeLimit: Duration(seconds: 10),
            ),
          );
        } catch (e) {
          debugPrint('获取位置失败: $e');
        }
      }
    } catch (e) {
      debugPrint('位置服务检查失败: $e');
    }
  }

  // 显示钓点详情
  void _showSpotDetails(FishingSpot spot) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => SpotDetailPage(spot: spot)),
    );
  }

  // 显示活动详情
  void _showActivityDetails(FishingActivity activity) {
    // 导航到活动详情页面，并处理返回结果
    Navigator.pushNamed(context, '/activity-detail', arguments: activity).then((
      result,
    ) {
      // 如果返回结果为true，表示需要刷新地图
      if (result == true) {
        debugPrint('🔄 [地图页面] 从活动详情页面返回，需要刷新');
        checkForUpdatesOnReturn();
      }
    });
  }

  // 计算屏幕25%高度处对应的地图坐标（使用工具类）
  LatLng _calculateLocationAt25PercentHeight() {
    final screenSize = MediaQuery.of(context).size;
    final camera = mapController.camera;

    return MapCoordinateUtils.calculateLocationAt25PercentHeight(
      camera,
      screenSize,
    );
  }

  // 计算分屏模式下的目标地图位置
  LatLng _calculateTargetLocationForSplitScreen(LatLng originalCenter) {
    final screenSize = MediaQuery.of(context).size;

    // 使用工具类计算目标位置
    return MapCoordinateUtils.calculateTargetLocationForScreenPosition(
      mapController.camera,
      originalCenter,
      screenHeightPercent: 0.25,
      screenWidthPercent: 0.50,
      screenSize: screenSize,
    );
  }

  /// 构建缓存的钓点标记
  Widget _buildCachedSpotMarker(FishingSpot spot, {required double size}) {
    final cacheKey = 'spot_${spot.id}';

    // 如果缓存中已存在，直接返回
    if (_spotMarkerCache.containsKey(cacheKey)) {
      return _spotMarkerCache[cacheKey]!;
    }

    // 创建新标记并缓存
    final marker = _buildSpotMarker(spot, size: size);
    _spotMarkerCache[cacheKey] = marker;
    return marker;
  }

  /// 构建缓存的活动标记
  Widget _buildCachedActivityMarker(
    FishingActivity activity, {
    required double size,
  }) {
    final cacheKey = 'activity_${activity.id}';

    debugPrint('🔍 [活动标记] 构建活动标记: ${activity.id} - ${activity.title}');
    debugPrint('🔍 [活动标记] 活动位置: ${activity.location}');
    debugPrint('🔍 [活动标记] 活动类型: ${activity.activityType}');

    // 如果缓存中已存在，直接返回
    if (_activityMarkerCache.containsKey(cacheKey)) {
      debugPrint('🔍 [活动标记] 使用缓存的活动标记: $cacheKey');
      return _activityMarkerCache[cacheKey]!;
    }

    // 创建新的活动标记并缓存
    debugPrint('🔍 [活动标记] 创建新的活动标记: $cacheKey');
    final marker = ActivityMarker(
      activity: activity,
      size: size,
      onTap: () => _showActivityDetails(activity),
    );
    _activityMarkerCache[cacheKey] = marker;
    return marker;
  }

  /// 构建钓点标记（使用照片标记）
  Widget _buildSpotMarker(
    FishingSpot spot, {
    required double size,
    bool isActivity = false,
    FishingActivity? originalActivity,
  }) {
    // 确定点击回调
    final onTap =
        isActivity && originalActivity != null
            ? () => _showActivityDetails(originalActivity)
            : () => _showSpotDetails(spot);

    // 统一使用照片标记
    return PhotoFishingSpotMarkerBuilder.buildMarker(
      spot: spot,
      onTap: onTap,
      size: size, //[*参数调整*]这个是位置标记的整体大小
      getPhotos: (spotId) => Services.fishingSpot.getSpotPhotos(spotId),
      getLikesCount: (spotId) => Services.social.getSpotLikesCount(spotId),
    );
  }

  /// 批量更新数据，避免频繁刷新
  void _batchUpdateData(
    List<FishingSpot> newSpots,
    List<FishingActivity> newActivities,
  ) {
    // 取消之前的定时器
    _batchUpdateTimer?.cancel();

    // 设置新的定时器，延迟批量更新
    _batchUpdateTimer = Timer(const Duration(milliseconds: 200), () {
      if (mounted) {
        setState(() {
          _spots.addAll(newSpots);

          // 添加新活动到列表，但要避免重复
          for (final newActivity in newActivities) {
            // 检查是否已存在相同ID的活动
            final existingIndex = _activities.indexWhere(
              (a) => a.id == newActivity.id,
            );
            if (existingIndex != -1) {
              // 如果已存在，替换为最新数据
              _activities[existingIndex] = newActivity;
              debugPrint(
                '🔄 [地图更新] 更新现有活动: ${newActivity.id} - ${newActivity.title}',
              );
            } else {
              // 如果不存在，添加新活动
              _activities.add(newActivity);
              debugPrint(
                '➕ [地图更新] 添加新活动: ${newActivity.id} - ${newActivity.title}',
              );
            }
          }
        });
      }
    });
  }

  /// 处理地图点击事件
  void _handleMapTap() {
    // 如果当前正在搜索状态，则退出搜索
    if (_isSearching) {
      // 通过搜索栏的key调用清除方法
      _searchBarKey.currentState?.clearSearchAndUnfocus();
      // 注意：_isSearching状态会通过onSearchEnded回调自动更新
    }
  }

  /// 显示登录提示对话框
  void _showLoginRequiredDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('需要登录'),
          content: const Text('添加钓点需要先登录账户。是否前往登录页面？'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // 导航到登录页面
                Navigator.of(context).pushNamed('/login');
              },
              child: const Text('去登录'),
            ),
          ],
        );
      },
    );
  }
}
