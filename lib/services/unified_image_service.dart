import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'encrypted_r2_service.dart';
import 'secure_image_upload_service.dart';
import 'image_cache_manager.dart';
import '../models/upload_result.dart';

/// 统一的图片服务
///
/// 功能：
/// 1. 统一的图片上传接口（头像、钓点照片等）
/// 2. 统一的签名URL生成
/// 3. 统一的图片显示组件
/// 4. 缓存管理
class UnifiedImageService {
  static final UnifiedImageService _instance = UnifiedImageService._internal();
  factory UnifiedImageService() => _instance;
  UnifiedImageService._internal();

  final SecureImageUploadService _uploadService = SecureImageUploadService();
  final EncryptedR2Service _r2Service = EncryptedR2Service();

  // 签名URL缓存
  final Map<String, String> _signedUrlCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const int _cacheExpiryMinutes = 50; // 缓存50分钟，签名URL有效期1小时

  /// 上传头像
  /// [imageFile] 图片文件
  /// [userId] 用户ID
  /// 返回上传结果
  Future<ImageUploadResult?> uploadAvatar({
    required File imageFile,
    required String userId,
  }) async {
    debugPrint('🖼️ [统一图片服务] 开始上传头像');
    return await _uploadService.uploadImageIndependent(
      imageFile: imageFile,
      userId: userId,
    );
  }

  /// 上传钓点照片
  /// [imageFile] 图片文件
  /// [userId] 用户ID
  /// [spotId] 钓点ID
  /// 返回上传结果
  Future<ImageUploadResult?> uploadSpotPhoto({
    required File imageFile,
    required String userId,
    required String spotId,
  }) async {
    debugPrint('🖼️ [统一图片服务] 开始上传钓点照片');
    return await _uploadService.uploadImageSecure(
      imageFile: imageFile,
      userId: userId,
      spotId: spotId,
    );
  }

  /// 获取签名URL
  /// [originalUrl] 原始R2 URL
  /// 返回签名URL，失败时返回原始URL
  Future<String> getSignedUrl(String originalUrl) async {
    try {
      // 检查缓存是否有效
      if (_signedUrlCache.containsKey(originalUrl) &&
          _cacheTimestamps.containsKey(originalUrl)) {
        final cacheTime = _cacheTimestamps[originalUrl]!;
        final now = DateTime.now();
        final cacheAgeMinutes = now.difference(cacheTime).inMinutes;

        if (cacheAgeMinutes < _cacheExpiryMinutes) {
          // final remainingMinutes = _cacheExpiryMinutes - cacheAgeMinutes;
          // debugPrint('✅ [签名URL缓存] 缓存命中: ${originalUrl.substring(0, 50)}...');
          // debugPrint(
          //   '⏰ [签名URL缓存] 缓存年龄: $cacheAgeMinutes分钟，剩余有效期: $remainingMinutes分钟',
          // );
          // debugPrint('🖼️ [统一图片服务] 使用缓存的签名URL');
          return _signedUrlCache[originalUrl]!;
        } else {
          // 缓存过期，清除
          _signedUrlCache.remove(originalUrl);
          _cacheTimestamps.remove(originalUrl);
          // debugPrint('❌ [签名URL缓存] 缓存过期: ${originalUrl.substring(0, 50)}...');
          // debugPrint(
          //   '⏰ [签名URL缓存] 缓存年龄: $cacheAgeMinutes分钟，已超过$_cacheExpiryMinutes分钟限制',
          // );
        }
      } else {
        // debugPrint('❌ [签名URL缓存] 缓存未命中: ${originalUrl.substring(0, 50)}...');
      }

      // 从原始URL中提取对象键
      final objectKey = _extractObjectKey(originalUrl);
      if (objectKey.isEmpty) {
        debugPrint('❌ [统一图片服务] 无法从URL中提取对象键: $originalUrl');
        return originalUrl; // 返回原始URL作为后备
      }

      debugPrint('🔍 [统一图片服务] 提取的对象键: $objectKey');

      // 生成签名URL
      final signedUrl = await _r2Service.generatePresignedUrl(
        objectKey: objectKey,
        method: 'GET',
        expiresIn: 3600, // 1小时有效期
      );

      if (signedUrl != null) {
        // 缓存签名URL
        _signedUrlCache[originalUrl] = signedUrl;
        _cacheTimestamps[originalUrl] = DateTime.now();
        // debugPrint(
        //   '💾 [签名URL缓存] 新签名URL已缓存: ${originalUrl.substring(0, 50)}...',
        // );
        // debugPrint('⏰ [签名URL缓存] 缓存有效期: $_cacheExpiryMinutes分钟');
        // debugPrint('📊 [签名URL缓存] 当前缓存数量: ${_signedUrlCache.length}');
        // debugPrint('✅ [统一图片服务] 签名URL生成成功');
        return signedUrl;
      } else {
        debugPrint('❌ [统一图片服务] 签名URL生成失败，使用原始URL');
        return originalUrl;
      }
    } catch (e) {
      debugPrint('❌ [统一图片服务] 签名URL生成异常: $e');
      return originalUrl; // 返回原始URL作为后备
    }
  }

  /// 从URL中提取对象键
  String _extractObjectKey(String originalUrl) {
    try {
      final uri = Uri.parse(originalUrl);
      final pathSegments = uri.pathSegments;

      debugPrint('🔍 [统一图片服务] 解析URL: $originalUrl');
      debugPrint('🔍 [统一图片服务] 路径段: $pathSegments');

      // 对于新的URL格式，需要去掉存储桶名称部分
      // 新格式: https://ab76374a6921dbc071ecdda63a033ec1.r2.cloudflarestorage.com/fishing_app/...
      // 我们需要提取 fishing_app 后面的部分作为对象键
      if (pathSegments.isNotEmpty) {
        // 查找存储桶名称的位置
        int bucketIndex = -1;
        for (int i = 0; i < pathSegments.length; i++) {
          if (pathSegments[i] == 'fishing_app' ||
              pathSegments[i] == 'fishing-app') {
            bucketIndex = i;
            break;
          }
        }

        String objectKey;
        if (bucketIndex >= 0 && bucketIndex < pathSegments.length - 1) {
          // 找到存储桶，提取后面的部分
          objectKey = pathSegments.sublist(bucketIndex + 1).join('/');
          debugPrint(
            '✅ [统一图片服务] 找到存储桶 ${pathSegments[bucketIndex]}，提取对象键: $objectKey',
          );
        } else {
          // 没找到存储桶标识，使用完整路径
          objectKey = pathSegments.join('/');
          debugPrint('🔍 [统一图片服务] 未找到存储桶标识，使用完整路径: $objectKey');
        }

        return objectKey;
      }

      debugPrint('❌ [统一图片服务] URL中没有路径段');
      return '';
    } catch (e) {
      debugPrint('❌ [统一图片服务] URL解析失败: $e');
      return '';
    }
  }

  /// 构建签名图片组件（带缓存）
  /// [originalUrl] 原始R2 URL
  /// [fit] 图片适配方式
  /// [width] 宽度
  /// [height] 高度
  /// [placeholder] 加载占位符
  /// [errorWidget] 错误显示组件
  /// [isAvatar] 是否为头像（使用不同的缓存管理器）
  /// [key] Widget的key，用于避免重复重建
  Widget buildCachedSignedImage({
    Key? key,
    required String originalUrl,
    BoxFit fit = BoxFit.cover,
    double? width,
    double? height,
    Widget? placeholder,
    Widget? errorWidget,
    bool isAvatar = false,
  }) {
    // 🚀 优化：先检查图片文件缓存，避免不必要的签名URL生成
    return FutureBuilder<bool>(
      key: key, // 🔧 添加key支持
      future: ImageCacheManager.isImageCached(originalUrl, isAvatar: isAvatar),
      builder: (context, cacheSnapshot) {
        if (cacheSnapshot.connectionState == ConnectionState.waiting) {
          return placeholder ??
              Container(
                width: width,
                height: height,
                color: Colors.grey.shade200,
                child: const Center(child: CircularProgressIndicator()),
              );
        }

        final isCached = cacheSnapshot.data ?? false;

        if (isCached) {
          // 🎯 缓存命中：直接使用CachedNetworkImage，它会从缓存加载
          // debugPrint(
          //   '🎯 [缓存优先] 图片已缓存，直接加载: ${originalUrl.substring(0, 50)}...',
          // );

          return CachedNetworkImage(
            key: key, // 🔧 传递key给CachedNetworkImage
            imageUrl: originalUrl, // 直接使用原始URL，CachedNetworkImage会处理
            cacheKey: originalUrl,
            cacheManager:
                isAvatar
                    ? ImageCacheManager.avatars
                    : ImageCacheManager.spotPhotos,
            fit: fit,
            width: width,
            height: height,
            placeholder: (context, url) {
              return placeholder ??
                  Container(
                    width: width,
                    height: height,
                    color: Colors.grey.shade200,
                    child: const Center(child: CircularProgressIndicator()),
                  );
            },
            imageBuilder: (context, imageProvider) {
              return Image(
                image: imageProvider,
                fit: fit,
                width: width,
                height: height,
              );
            },
            errorWidget: (context, url, error) {
              // 如果缓存加载失败，回退到签名URL下载
              return _buildSignedImageWidget(
                originalUrl: originalUrl,
                fit: fit,
                width: width,
                height: height,
                placeholder: placeholder,
                errorWidget: errorWidget,
                isAvatar: isAvatar,
              );
            },
            fadeInDuration: const Duration(milliseconds: 300),
            fadeOutDuration: const Duration(milliseconds: 100),
          );
        } else {
          // 🌐 缓存未命中：需要生成签名URL进行下载
          debugPrint(
            '🌐 [缓存优先] 图片未缓存，生成签名URL下载: ${originalUrl.substring(0, 50)}...',
          );

          return _buildSignedImageWidget(
            originalUrl: originalUrl,
            fit: fit,
            width: width,
            height: height,
            placeholder: placeholder,
            errorWidget: errorWidget,
            isAvatar: isAvatar,
          );
        }
      },
    );
  }

  /// 构建签名图片组件（兼容旧版本，无缓存）
  /// [originalUrl] 原始R2 URL
  /// [fit] 图片适配方式
  /// [width] 宽度
  /// [height] 高度
  /// [placeholder] 加载占位符
  /// [errorWidget] 错误显示组件
  Widget buildSignedImage({
    required String originalUrl,
    BoxFit fit = BoxFit.cover,
    double? width,
    double? height,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    // 默认使用缓存版本
    return buildCachedSignedImage(
      originalUrl: originalUrl,
      fit: fit,
      width: width,
      height: height,
      placeholder: placeholder,
      errorWidget: errorWidget,
      isAvatar: false,
    );
  }

  /// 构建错误显示组件
  Widget _buildErrorWidget(
    double? width,
    double? height, {
    Object? error,
    VoidCallback? onRetry,
  }) {
    // 分析错误类型，提供更友好的错误提示
    String errorMessage = '图片加载失败';
    IconData errorIcon = Icons.broken_image;
    bool showRetry = false;

    if (error != null) {
      final errorStr = error.toString();
      if (errorStr.contains('Connection timed out') ||
          errorStr.contains('SocketException') ||
          errorStr.contains('ClientException')) {
        errorMessage = '网络连接超时';
        errorIcon = Icons.wifi_off;
        showRetry = true;
      } else if (errorStr.contains('404') || errorStr.contains('Not Found')) {
        errorMessage = '图片不存在';
        errorIcon = Icons.image_not_supported;
      } else if (errorStr.contains('403') || errorStr.contains('Forbidden')) {
        errorMessage = '访问被拒绝';
        errorIcon = Icons.lock;
      }
    }

    return Container(
      width: width,
      height: height,
      color: Colors.grey.shade200,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(errorIcon, size: 48, color: Colors.grey.shade400),
            const SizedBox(height: 8),
            Text(
              errorMessage,
              style: TextStyle(color: Colors.grey.shade500, fontSize: 12),
              textAlign: TextAlign.center,
            ),
            if (showRetry && onRetry != null) ...[
              const SizedBox(height: 8),
              TextButton.icon(
                onPressed: onRetry,
                icon: Icon(
                  Icons.refresh,
                  size: 16,
                  color: Colors.blue.shade600,
                ),
                label: Text(
                  '重试',
                  style: TextStyle(color: Colors.blue.shade600, fontSize: 12),
                ),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  minimumSize: const Size(0, 0),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建签名头像组件（带缓存）
  /// [originalUrl] 原始R2 URL
  /// [radius] 头像半径
  /// [backgroundColor] 背景色
  /// [placeholderIcon] 占位符图标
  Widget buildSignedAvatar({
    required String originalUrl,
    required double radius,
    Color? backgroundColor,
    IconData placeholderIcon = Icons.person,
  }) {
    return FutureBuilder<String>(
      future: getSignedUrl(originalUrl),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return CircleAvatar(
            radius: radius,
            backgroundColor:
                backgroundColor ?? Colors.white.withValues(alpha: 0.3),
            child: const CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          );
        }

        if (snapshot.hasError || !snapshot.hasData) {
          return CircleAvatar(
            radius: radius,
            backgroundColor:
                backgroundColor ?? Colors.white.withValues(alpha: 0.3),
            child: Icon(
              placeholderIcon,
              size: radius * 0.8,
              color: Colors.white,
            ),
          );
        }

        final signedUrl = snapshot.data!;
        debugPrint('🖼️ [缓存头像] 加载头像: $signedUrl');

        // 🔧 关键修复：使用原始URL作为缓存键，而不是签名URL
        ImageCacheManager.isImageCached(originalUrl, isAvatar: true)
            .then((isCached) {
              if (isCached) {
                debugPrint(
                  '🎯 [缓存头像] 头像已在缓存中，将从缓存加载: ${originalUrl.substring(0, 50)}...',
                );
              } else {
                debugPrint(
                  '🌐 [缓存头像] 头像不在缓存中，将从网络下载: ${originalUrl.substring(0, 50)}...',
                );
              }
            })
            .catchError((e) {
              debugPrint('⚠️ [缓存头像] 检查缓存状态失败: $e');
            });

        return CachedNetworkImage(
          imageUrl: signedUrl,
          cacheKey: originalUrl, // 🔧 关键修复：使用原始URL作为缓存键
          cacheManager: ImageCacheManager.avatars,
          imageBuilder: (context, imageProvider) {
            debugPrint(
              '✅ [缓存头像] 头像加载完成（可能来自缓存）: ${signedUrl.substring(0, 50)}...',
            );
            return CircleAvatar(
              radius: radius,
              backgroundColor:
                  backgroundColor ?? Colors.white.withValues(alpha: 0.3),
              backgroundImage: imageProvider,
            );
          },
          placeholder: (context, url) {
            debugPrint('🔄 [缓存头像] 正在加载头像: ${url.substring(0, 50)}...');
            return CircleAvatar(
              radius: radius,
              backgroundColor:
                  backgroundColor ?? Colors.white.withValues(alpha: 0.3),
              child: const CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            );
          },
          errorWidget: (context, url, error) {
            debugPrint('❌ [缓存头像] 头像加载失败: $url - $error');
            return CircleAvatar(
              radius: radius,
              backgroundColor:
                  backgroundColor ?? Colors.white.withValues(alpha: 0.3),
              child: Icon(
                placeholderIcon,
                size: radius * 0.8,
                color: Colors.white,
              ),
            );
          },
          fadeInDuration: const Duration(milliseconds: 300),
          fadeOutDuration: const Duration(milliseconds: 100),
        );
      },
    );
  }

  /// 清除签名URL缓存
  void clearSignedUrlCache() {
    final cacheCount = _signedUrlCache.length;
    _signedUrlCache.clear();
    _cacheTimestamps.clear();
    debugPrint('🧹 [统一图片服务] 签名URL缓存已清除，清除了 $cacheCount 个缓存项');
  }

  /// 获取签名URL缓存统计信息
  Map<String, dynamic> getSignedUrlCacheStats() {
    final now = DateTime.now();
    int validCount = 0;
    int expiredCount = 0;

    for (final entry in _cacheTimestamps.entries) {
      final cacheAgeMinutes = now.difference(entry.value).inMinutes;
      if (cacheAgeMinutes < _cacheExpiryMinutes) {
        validCount++;
      } else {
        expiredCount++;
      }
    }

    return {
      'totalCached': _signedUrlCache.length,
      'validCached': validCount,
      'expiredCached': expiredCount,
      'cacheExpiryMinutes': _cacheExpiryMinutes,
    };
  }

  /// 调试方法：打印签名URL缓存状态
  void debugSignedUrlCacheStatus() {
    final stats = getSignedUrlCacheStats();
    debugPrint('🔍 [签名URL缓存] === 缓存状态调试 ===');
    debugPrint('🔍 [签名URL缓存] 总缓存数: ${stats['totalCached']}');
    debugPrint('🔍 [签名URL缓存] 有效缓存: ${stats['validCached']}');
    debugPrint('🔍 [签名URL缓存] 过期缓存: ${stats['expiredCached']}');
    debugPrint('🔍 [签名URL缓存] 缓存有效期: ${stats['cacheExpiryMinutes']}分钟');
    debugPrint('🔍 [签名URL缓存] === 调试完成 ===');
  }

  /// 清除过期的签名URL缓存
  void clearExpiredSignedUrlCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    for (final entry in _cacheTimestamps.entries) {
      if (now.difference(entry.value).inMinutes >= _cacheExpiryMinutes) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _signedUrlCache.remove(key);
      _cacheTimestamps.remove(key);
    }

    if (expiredKeys.isNotEmpty) {
      debugPrint('🧹 [统一图片服务] 清除了 ${expiredKeys.length} 个过期签名URL缓存');
    }
  }

  /// 清除所有图片缓存（包括签名URL和文件缓存）
  Future<void> clearAllCache() async {
    // 清除签名URL缓存
    clearSignedUrlCache();

    // 清除图片文件缓存
    await ImageCacheManager.clearAllCache();

    debugPrint('🧹 [统一图片服务] 所有缓存已清除');
  }

  /// 清除钓点照片缓存
  Future<void> clearSpotPhotosCache() async {
    await ImageCacheManager.clearSpotPhotosCache();
    debugPrint('🧹 [统一图片服务] 钓点照片缓存已清除');
  }

  /// 清除头像缓存
  Future<void> clearAvatarsCache() async {
    await ImageCacheManager.clearAvatarsCache();
    debugPrint('🧹 [统一图片服务] 头像缓存已清除');
  }

  /// 获取缓存统计信息
  Future<Map<String, dynamic>> getCacheStats() async {
    final imageCacheStats = await ImageCacheManager.getCacheStats();

    return {
      'signedUrlCache': {
        'count': _signedUrlCache.length,
        'expiryMinutes': _cacheExpiryMinutes,
      },
      'imageCache': imageCacheStats,
    };
  }

  /// 预加载图片
  Future<void> preloadImage(String originalUrl, {bool isAvatar = false}) async {
    try {
      final signedUrl = await getSignedUrl(originalUrl);
      await ImageCacheManager.preloadImage(signedUrl, isAvatar: isAvatar);
      debugPrint('✅ [统一图片服务] 图片预加载完成: $originalUrl');
    } catch (e) {
      debugPrint('❌ [统一图片服务] 图片预加载失败: $originalUrl - $e');
    }
  }

  /// 检查图片是否已缓存
  Future<bool> isImageCached(
    String originalUrl, {
    bool isAvatar = false,
  }) async {
    try {
      final signedUrl = await getSignedUrl(originalUrl);
      return await ImageCacheManager.isImageCached(
        signedUrl,
        isAvatar: isAvatar,
      );
    } catch (e) {
      debugPrint('❌ [统一图片服务] 检查缓存状态失败: $originalUrl - $e');
      return false;
    }
  }

  /// 兼容旧版本的clearCache方法
  @Deprecated('使用 clearSignedUrlCache() 或 clearAllCache() 替代')
  void clearCache() {
    clearSignedUrlCache();
  }

  /// 兼容旧版本的clearExpiredCache方法
  @Deprecated('使用 clearExpiredSignedUrlCache() 替代')
  void clearExpiredCache() {
    clearExpiredSignedUrlCache();
  }

  /// 构建需要签名URL的图片组件（内部方法）
  Widget _buildSignedImageWidget({
    required String originalUrl,
    BoxFit fit = BoxFit.cover,
    double? width,
    double? height,
    Widget? placeholder,
    Widget? errorWidget,
    bool isAvatar = false,
  }) {
    return FutureBuilder<String>(
      future: getSignedUrl(originalUrl),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return placeholder ??
              Container(
                width: width,
                height: height,
                color: Colors.grey.shade200,
                child: const Center(child: CircularProgressIndicator()),
              );
        }

        if (snapshot.hasError || !snapshot.hasData) {
          debugPrint('❌ [签名下载] 获取签名URL失败: ${snapshot.error}');
          return errorWidget ??
              _buildErrorWidget(
                width,
                height,
                error: snapshot.error,
                onRetry: () {
                  // 触发重新构建来重试
                  (context as Element).markNeedsBuild();
                },
              );
        }

        final signedUrl = snapshot.data!;
        debugPrint(
          '🖼️ [签名下载] 使用签名URL下载图片: ${originalUrl.substring(0, 50)}...',
        );

        return CachedNetworkImage(
          imageUrl: signedUrl,
          cacheKey: originalUrl, // 使用原始URL作为缓存键
          cacheManager:
              isAvatar
                  ? ImageCacheManager.avatars
                  : ImageCacheManager.spotPhotos,
          fit: fit,
          width: width,
          height: height,
          placeholder: (context, url) {
            debugPrint('🔄 [签名下载] 正在下载图片: ${originalUrl.substring(0, 50)}...');
            return placeholder ??
                Container(
                  width: width,
                  height: height,
                  color: Colors.grey.shade200,
                  child: const Center(child: CircularProgressIndicator()),
                );
          },
          imageBuilder: (context, imageProvider) {
            debugPrint(
              '✅ [签名下载] 图片下载并缓存完成: ${originalUrl.substring(0, 50)}...',
            );
            return Image(
              image: imageProvider,
              fit: fit,
              width: width,
              height: height,
            );
          },
          errorWidget: (context, url, error) {
            debugPrint('❌ [签名下载] 图片下载失败: $url - $error');
            return errorWidget ??
                _buildErrorWidget(
                  width,
                  height,
                  error: error,
                  onRetry: () {
                    // 触发重新构建来重试
                    (context as Element).markNeedsBuild();
                  },
                );
          },
          fadeInDuration: const Duration(milliseconds: 300),
          fadeOutDuration: const Duration(milliseconds: 100),
        );
      },
    );
  }
}
