import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';

import 'models/fishing_activity.dart';
import 'widgets/activity_marker.dart';

/// 测试ActivityMarker组件的页面
class TestActivityMarkerPage extends StatelessWidget {
  const TestActivityMarkerPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('活动标记测试'), backgroundColor: Colors.blue),
      body: Container(
        color: Colors.green.shade100,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                '活动标记组件测试',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 40),

              // 测试不同类型的活动标记
              Wrap(
                spacing: 20,
                runSpacing: 20,
                children: [
                  _buildTestMarker('路亚活动', 'lure'),
                  _buildTestMarker('台钓活动', 'platform'),
                  _buildTestMarker('夜钓活动', 'night'),
                  _buildTestMarker('鱼塘活动', 'pond'),
                  _buildTestMarker('海钓活动', 'sea'),
                ],
              ),

              const SizedBox(height: 40),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('返回'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTestMarker(String label, String activityType) {
    final testActivity = FishingActivity(
      id: 'test_$activityType',
      title: label,
      description: '这是一个$label测试',
      location: {'lat': 39.9042, 'lon': 116.4074},
      startTime: DateTime.now().add(const Duration(hours: 2)),
      duration: 3.0,
      maxParticipants: 8,
      currentParticipants: 3,
      creatorId: 'test_user_$activityType',
      creatorName: '测试用户',
      created: DateTime.now(),
      updated: DateTime.now(),
      activityType: activityType,
    );

    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ActivityMarker(
            activity: testActivity,
            size: 60,
            onTap: () {
              // 测试点击功能
              debugPrint('点击了$label');
            },
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
        ),
      ],
    );
  }
}
