import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';

import 'widgets/split_screen_add_activity.dart';

/// 测试升级后的添加活动页面
class TestUpgradePage extends StatelessWidget {
  const TestUpgradePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('测试升级后的添加活动页面'),
      ),
      body: Stack(
        children: [
          // 模拟地图背景
          Container(
            color: Colors.green.shade100,
            child: const Center(
              child: Text(
                '地图区域\n(模拟)',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 24,
                  color: Colors.green,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          
          // 添加活动组件
          SplitScreenAddActivity(
            location: const LatLng(39.9042, 116.4074), // 北京坐标
            suggestedName: '测试钓点',
            onLocationChanged: (location) {
              debugPrint('位置更新: $location');
            },
            onClose: () {
              Navigator.of(context).pop();
            },
            onActivityAdded: (activity) {
              debugPrint('活动添加成功: ${activity.title}');
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('活动 "${activity.title}" 创建成功！'),
                  backgroundColor: Colors.green,
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
