import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../snackbar.dart';

/// 钓鱼时间选择器
///
/// 用于选择约钓活动的开始时间和持续时长
class FishingTimeSelector extends StatefulWidget {
  /// 当前选中的开始时间
  final DateTime? selectedTime;

  /// 时间变更回调
  final Function(DateTime) onTimeChanged;

  /// 是否必填
  final bool required;

  const FishingTimeSelector({
    super.key,
    this.selectedTime,
    required this.onTimeChanged,
    this.required = true,
  });

  @override
  State<FishingTimeSelector> createState() => _FishingTimeSelectorState();
}

class _FishingTimeSelectorState extends State<FishingTimeSelector> {
  DateTime? _selectedDateTime;
  final DateFormat _dateFormat = DateFormat('yyyy年MM月dd日');
  final DateFormat _timeFormat = DateFormat('HH:mm');

  @override
  void initState() {
    super.initState();
    _selectedDateTime = widget.selectedTime ?? _getDefaultTime();
  }

  /// 获取默认时间（当前时间后1小时10分钟，整点）
  DateTime _getDefaultTime() {
    final now = DateTime.now();
    final minTime = now.add(const Duration(hours: 1, minutes: 10));
    return DateTime(
      minTime.year,
      minTime.month,
      minTime.day,
      minTime.hour,
      0, // 分钟设为0，整点时间
    );
  }

  /// 显示日期选择器
  Future<void> _selectDate() async {
    final now = DateTime.now();
    final initialDate = _selectedDateTime ?? _getDefaultTime();

    final minAllowedTime = now.add(const Duration(hours: 1, minutes: 10));
    final selectedDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: minAllowedTime, // 不能选择早于最小允许时间的日期
      lastDate: now.add(const Duration(days: 30)), // 最多30天后
      locale: const Locale('zh', 'CN'), // 使用中文显示
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Colors.blue.shade600,
              onPrimary: Colors.white,
            ),
          ),
          child: child!,
        );
      },
    );

    if (selectedDate != null) {
      final newDateTime = DateTime(
        selectedDate.year,
        selectedDate.month,
        selectedDate.day,
        _selectedDateTime?.hour ?? _getDefaultTime().hour,
        _selectedDateTime?.minute ?? 0,
      );

      final now = DateTime.now();
      final minAllowedTime = now.add(const Duration(hours: 1, minutes: 10));

      // 如果选择的日期时间早于最小允许时间，自动调整为最小允许时间
      if (newDateTime.isBefore(minAllowedTime)) {
        setState(() {
          _selectedDateTime = DateTime(
            selectedDate.year,
            selectedDate.month,
            selectedDate.day,
            minAllowedTime.hour,
            0, // 分钟设为0，整点时间
          );
        });
        // 提示用户时间已被自动调整
        SnackBarService.showInfo(
          context, 
          '时间已自动调整为${minAllowedTime.hour}:00，确保活动至少提前1小时10分钟安排'
        );
      } else {
        setState(() {
          _selectedDateTime = newDateTime;
        });
      }

      widget.onTimeChanged(_selectedDateTime!);
    }
  }

  /// 显示时间选择器
  Future<void> _selectTime() async {
    final initialTime = TimeOfDay.fromDateTime(
      _selectedDateTime ?? _getDefaultTime(),
    );

    final selectedTime = await showTimePicker(
      context: context,
      initialTime: initialTime,
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
          child: Theme(
            data: Theme.of(context).copyWith(
              colorScheme: Theme.of(context).colorScheme.copyWith(
                primary: Colors.blue.shade600,
                onPrimary: Colors.white,
              ),
            ),
            child: child!,
          ),
        );
      },
    );

    if (selectedTime != null) {
      final currentDate = _selectedDateTime ?? _getDefaultTime();
      final newDateTime = DateTime(
        currentDate.year,
        currentDate.month,
        currentDate.day,
        selectedTime.hour,
        selectedTime.minute,
      );

      final now = DateTime.now();
      final minAllowedTime = now.add(const Duration(hours: 1, minutes: 10));

      // 如果选择的时间早于最小允许时间，显示提示并不更新时间
      if (newDateTime.isBefore(minAllowedTime)) {
        SnackBarService.showWarning(
          context, 
          '活动开始时间需要至少提前1小时10分钟安排，请重新选择时间'
        );
        return;
      }

      setState(() {
        _selectedDateTime = newDateTime;
      });
      widget.onTimeChanged(_selectedDateTime!);
    }
  }

  /// 获取时间状态描述
  String _getTimeStatusDescription() {
    if (_selectedDateTime == null) return '';

    final now = DateTime.now();
    final difference = _selectedDateTime!.difference(now);

    if (difference.inMinutes < 0) {
      return '设置的时间已过期，请重新选择';
    } else {
      final days = difference.inDays;
      final hours = difference.inHours % 24;

      if (days > 0) {
        return '距离开始钓鱼还有${days}天${hours}小时';
      } else if (hours > 0) {
        return '距离开始钓鱼还有${hours}小时';
      } else {
        return ''; // 移除"即将开始"标签，返回空字符串
      }
    }
  }

  /// 获取时间状态颜色
  // Color _getTimeStatusColor() {
  //   if (_selectedDateTime == null) return Colors.grey;

  //   final now = DateTime.now();
  //   final difference = _selectedDateTime!.difference(now);

  //   if (difference.inMinutes < 0) {
  //     return Colors.red;
  //   } else if (difference.inHours < 1) {
  //     return Colors.orange;
  //   } else {
  //     return Colors.green;
  //   }
  // }



  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Row(
          children: [
            const Icon(Icons.access_time, size: 20, color: Colors.blue),
            const SizedBox(width: 8),
            const Text(
              '钓鱼时间',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            if (widget.required)
              const Text(
                ' *',
                style: TextStyle(color: Colors.red, fontSize: 16),
              ),
          ],
        ),

        const SizedBox(height: 12),

        // 时间选择按钮
        Row(
          children: [
            // 日期选择
            Expanded(
              flex: 2,
              child: InkWell(
                onTap: _selectDate,
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 14,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300, width: 1.5),
                    borderRadius: BorderRadius.circular(12),
                    color: Colors.white,
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        size: 18,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _selectedDateTime != null
                              ? _dateFormat.format(_selectedDateTime!)
                              : '选择日期',
                          style: TextStyle(
                            fontSize: 14,
                            color:
                                _selectedDateTime != null
                                    ? Colors.black87
                                    : Colors.grey.shade500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            const SizedBox(width: 12),

            // 时间选择
            Expanded(
              flex: 1,
              child: InkWell(
                onTap: _selectTime,
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 14,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300, width: 1.5),
                    borderRadius: BorderRadius.circular(12),
                    color: Colors.white,
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.schedule,
                        size: 18,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _selectedDateTime != null
                              ? _timeFormat.format(_selectedDateTime!)
                              : '时间',
                          style: TextStyle(
                            fontSize: 14,
                            color:
                                _selectedDateTime != null
                                    ? Colors.black87
                                    : Colors.grey.shade500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // // 时间状态提示
        // if (_selectedDateTime != null)
        //   Container(
        //     padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        //     decoration: BoxDecoration(
        //       color: _getTimeStatusColor().withValues(alpha: 0.1),
        //       borderRadius: BorderRadius.circular(8),
        //       border: Border.all(
        //         color: _getTimeStatusColor().withValues(alpha: 0.3),
        //         width: 1,
        //       ),
        //     ),
        //     child: Row(
        //       mainAxisSize: MainAxisSize.min,
        //       children: [
        //         Icon(
        //           Icons.info_outline,
        //           size: 14,
        //           color: _getTimeStatusColor(),
        //         ),
        //         const SizedBox(width: 4),
        //         Text(
        //           _getTimeStatusDescription(),
        //           style: TextStyle(
        //             fontSize: 12,
        //             color: _getTimeStatusColor(),
        //             fontWeight: FontWeight.w500,
        //           ),
        //         ),
        //       ],
        //     ),
        //   ),


      ],
    );
  }
}
