import 'dart:async';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';

import '../../theme/unified_theme.dart';
import '../../services/location_verification_service.dart';
import '../../services/service_locator.dart';
import '../../config/pocketbase_config.dart';
import '../../models/spot_visibility.dart';
import '../snackbar.dart';

import 'spot_name_input.dart';
import 'spot_description_input.dart';
import 'water_level_selector.dart';
import 'bait_selector.dart';
import 'fish_type_selector.dart';
import 'image_upload_widget.dart';
import 'image_upload_manager.dart';
import 'verification_status_widget.dart';
import '../spot_visibility_selector.dart';

/// 重构后的添加钓点表单组件
///
/// 功能：
/// - 模块化的表单组件
/// - 实时位置验证
/// - 即时图片上传
/// - 现代化UI设计
class AddSpotForm extends StatefulWidget {
  /// 钓点位置
  final LatLng location;

  /// 建议的钓点名称
  final String? suggestedName;

  /// 提交成功回调
  final Function(String spotId) onSubmitSuccess;

  /// 取消回调
  final VoidCallback? onCancel;

  const AddSpotForm({
    super.key,
    required this.location,
    this.suggestedName,
    required this.onSubmitSuccess,
    this.onCancel,
  });

  @override
  State<AddSpotForm> createState() => _AddSpotFormState();
}

class _AddSpotFormState extends State<AddSpotForm> {
  final _formKey = GlobalKey<FormState>();
  final _imageUploadManager = ImageUploadManager();

  // 表单控制器
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  // 表单状态
  String _selectedWaterLevel = '正常';
  String _selectedBait = '商品饵';
  String _selectedFishType = 'carp'; // 默认鲤鱼
  SpotVisibility _selectedVisibility = SpotVisibility.public;
  final List<ImageUploadItem> _selectedImages = [];

  // 位置验证状态
  Position? _publishLocation;
  bool _isOnSite = false;
  bool _isCameraShot = false;
  StreamSubscription<Position>? _locationSubscription;

  // UI状态
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _initializeLocationVerification();

    // 如果有建议名称，设置到输入框
    if (widget.suggestedName != null && widget.suggestedName!.isNotEmpty) {
      _nameController.text = widget.suggestedName!;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _locationSubscription?.cancel();
    super.dispose();
  }

  /// 初始化位置验证
  Future<void> _initializeLocationVerification() async {
    // 获取当前位置
    final position = await LocationVerificationService.getCurrentPosition();
    if (position != null) {
      setState(() {
        _publishLocation = position;
        _isOnSite = LocationVerificationService.isOnSite(
          widget.location,
          position,
        );
      });

      // 监听位置变化
      _locationSubscription =
          LocationVerificationService.listenToLocationChanges(widget.location, (
            isOnSite,
            position,
          ) {
            if (mounted) {
              setState(() {
                _publishLocation = position;
                _isOnSite = isOnSite;
              });
            }
          });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('添加钓点'),
        backgroundColor: AppTheme.surfaceColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: widget.onCancel,
        ),
        actions: [
          TextButton(
            onPressed: _canSubmit() ? _submitForm : null,
            child: Text(
              _imageUploadManager.hasUploadingImages(_selectedImages)
                  ? _imageUploadManager.getUploadStatusText(_selectedImages)
                  : '发布',
              style: AppTheme.labelLarge.copyWith(
                color:
                    _canSubmit()
                        ? AppTheme.designPrimaryColor
                        : AppTheme.textHintColor,
              ),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppTheme.spacingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 钓点名称
              _buildFormCard(
                child: SpotNameInput(
                  controller: _nameController,
                  location: widget.location,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return '请输入钓点名称';
                    }
                    return null;
                  },
                ),
              ),

              const SizedBox(height: AppTheme.spacingM),

              // 图片上传
              _buildFormCard(
                child: ImageUploadWidget(
                  selectedImages: _selectedImages,
                  onImagesAdded: _handleImagesAdded,
                  onImageRemoved: _handleImageRemoved,
                ),
              ),

              const SizedBox(height: AppTheme.spacingM),

              // 钓点描述
              _buildFormCard(
                child: SpotDescriptionInput(
                  controller: _descriptionController,
                  labelText: '钓点描述',
                  hintText: '描述钓点的特色、环境、注意事项等...',
                  showCharacterCount: true,
                  minLength: 15,
                  requireInput: true,
                ),
              ),

              const SizedBox(height: AppTheme.spacingM),

              // 水位、饵料和鱼种选择
              _buildFormCard(
                child: Column(
                  children: [
                    WaterLevelSelector(
                      selectedWaterLevel: _selectedWaterLevel,
                      onChanged:
                          (value) =>
                              setState(() => _selectedWaterLevel = value),
                    ),
                    const SizedBox(height: AppTheme.spacingL),
                    BaitSelector(
                      selectedBait: _selectedBait,
                      onChanged:
                          (value) => setState(() => _selectedBait = value),
                    ),
                    const SizedBox(height: AppTheme.spacingL),
                    FishTypeSelector(
                      selectedFishType: _selectedFishType,
                      onChanged:
                          (value) => setState(() => _selectedFishType = value),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppTheme.spacingM),

              // 可见性设置
              _buildFormCard(
                child: SpotVisibilitySelector(
                  initialVisibility: _selectedVisibility,
                  onChanged:
                      (value, conditions) =>
                          setState(() => _selectedVisibility = value),
                ),
              ),

              const SizedBox(height: AppTheme.spacingM),

              // 验证状态
              _buildFormCard(
                child: VerificationStatusWidget(
                  isOnSite: _isOnSite,
                  isCameraShot: _isCameraShot,
                  publishLocation:
                      _publishLocation != null
                          ? LocationVerificationService.createPublishLocationInfo(
                            widget.location,
                            _publishLocation!,
                          )
                          : null,
                  hasImages: _selectedImages.isNotEmpty,
                ),
              ),

              const SizedBox(height: AppTheme.spacingXL),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建表单卡片
  Widget _buildFormCard({required Widget child}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: AppTheme.cardDecoration,
      child: child,
    );
  }

  /// 处理图片添加
  void _handleImagesAdded(List<ImageUploadItem> newImages) {
    setState(() {
      _selectedImages.addAll(newImages);
      _isCameraShot = _imageUploadManager.updateCameraShotStatus(
        _selectedImages,
      );
    });

    // 立即开始上传
    _imageUploadManager.uploadSelectedImages(newImages, (updatedItem) {
      if (mounted) {
        setState(() {
          // 触发UI更新
        });
      }
    });
  }

  /// 处理图片删除
  void _handleImageRemoved(int index) {
    setState(() {
      _selectedImages.removeAt(index);
      _isCameraShot = _imageUploadManager.updateCameraShotStatus(
        _selectedImages,
      );
    });
  }

  /// 检查是否可以提交
  bool _canSubmit() {
    return !_isSubmitting &&
        !_imageUploadManager.hasUploadingImages(_selectedImages) &&
        _nameController.text.trim().isNotEmpty;
  }

  /// 提交表单
  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;
    if (_isSubmitting) return;

    setState(() => _isSubmitting = true);

    try {
      // 创建钓点记录
      final spotId = await _createSpotRecord();

      // 关联已上传的图片
      if (_selectedImages.isNotEmpty) {
        final uploadedImages =
            _selectedImages.where((item) => item.isCompleted).toList();
        if (uploadedImages.isNotEmpty) {
          await _imageUploadManager.associateUploadedImages(
            spotId,
            Services.auth.currentUser!.id,
            uploadedImages,
          );
        }
      }

      // 成功回调
      widget.onSubmitSuccess(spotId);
    } catch (e) {
      if (mounted) {
        SnackBarService.showError(context, '发布失败: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  /// 创建钓点记录
  Future<String> _createSpotRecord() async {
    final pb = PocketBaseConfig.instance.client;
    final user = Services.auth.currentUser!;

    final spotData = {
      'name': _nameController.text.trim(),
      'description': _descriptionController.text.trim(),
      'latitude': widget.location.latitude,
      'longitude': widget.location.longitude,
      'water_level': _selectedWaterLevel,
      'bait': _selectedBait,
      'fish_types': _selectedFishType,
      'visibility': _selectedVisibility.name,
      'user_id': user.id,
      'is_on_site': _isOnSite,
      'is_camera_shot': _isCameraShot,
    };

    // 添加位置验证信息
    if (_publishLocation != null) {
      final locationInfo =
          LocationVerificationService.createPublishLocationInfo(
            widget.location,
            _publishLocation!,
          );
      for (final entry in locationInfo.entries) {
        spotData[entry.key] = entry.value;
      }
    }

    final record = await pb.collection('fishing_spots').create(body: spotData);
    return record.id;
  }
}
