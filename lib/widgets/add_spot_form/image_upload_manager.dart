import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import '../../services/service_locator.dart';
import '../../services/image_upload_service.dart';
import '../../config/pocketbase_config.dart';
import 'image_upload_widget.dart';

/// 图片上传管理器
///
/// 功能：
/// - 管理图片上传流程
/// - 处理上传进度
/// - 保存照片记录到数据库
/// - 验证实拍状态
class ImageUploadManager {
  final ImageUploadService _imageUploadService = ImageUploadService();

  /// 上传选中的图片（即时上传）
  Future<void> uploadSelectedImages(
    List<ImageUploadItem> items,
    Function(ImageUploadItem) onItemUpdated,
  ) async {
    // 检查用户是否已登录
    if (!Services.auth.isLoggedIn) {
      // 如果未登录，将图片标记为等待上传状态
      for (var item in items) {
        item.errorMessage = '请先登录';
        onItemUpdated(item);
      }
      return;
    }

    final user = Services.auth.currentUser;
    if (user == null) return;

    // 立即开始上传每张图片
    for (var item in items) {
      _uploadSingleImage(item, user.id, onItemUpdated);
    }
  }

  /// 上传单张图片
  Future<void> _uploadSingleImage(
    ImageUploadItem item,
    String userId,
    Function(ImageUploadItem) onItemUpdated,
  ) async {
    try {
      // 标记为正在上传
      item.isUploading = true;
      item.uploadProgress = 0.0;
      item.errorMessage = null;
      item.isCancelled = false;
      onItemUpdated(item);

      // 创建取消控制器
      final canceller = Completer<void>();
      item.setUploadCanceller(canceller);

      // 模拟上传进度（因为实际上传没有进度回调）
      _simulateUploadProgress(item, onItemUpdated);

      // 使用 Future.any 来支持取消功能
      final result = await Future.any([
        _imageUploadService.uploadImageIndependent(
          imageFile: item.file,
          userId: userId,
        ),
        canceller.future.then((_) => null), // 取消时返回 null
      ]);

      // 检查是否被取消
      if (item.isCancelled) {
        debugPrint('⚠️ [即时上传] 图片上传已取消');
        return;
      }

      if (result != null) {
        // 上传成功
        item.isCompleted = true;
        item.isUploading = false;
        item.uploadProgress = 1.0;
        item.uploadedUrl = result.originalUrl;
        item.thumbnailUrl = result.thumbnailUrl;
        onItemUpdated(item);
        
        debugPrint('✅ [即时上传] 图片上传成功: ${result.originalUrl}');
        debugPrint('✅ [即时上传] 缩略图URL: ${result.thumbnailUrl}');
      } else {
        // 上传失败
        item.isUploading = false;
        item.uploadProgress = 0.0;
        item.errorMessage = '上传失败';
        onItemUpdated(item);
        
        debugPrint('❌ [即时上传] 图片上传失败');
      }
    } catch (e) {
      // 检查是否是取消导致的异常
      if (item.isCancelled) {
        debugPrint('⚠️ [即时上传] 图片上传已取消');
        return;
      }
      
      // 上传异常
      item.isUploading = false;
      item.uploadProgress = 0.0;
      item.errorMessage = '上传异常: $e';
      onItemUpdated(item);
      
      debugPrint('❌ [即时上传] 图片上传异常: $e');
    }
  }

  /// 模拟上传进度
  void _simulateUploadProgress(
    ImageUploadItem item,
    Function(ImageUploadItem) onItemUpdated,
  ) {
    Timer.periodic(const Duration(milliseconds: 200), (timer) {
      if (!item.isUploading || item.isCompleted) {
        timer.cancel();
        return;
      }

      item.uploadProgress += 0.1;
      if (item.uploadProgress >= 0.9) {
        item.uploadProgress = 0.9; // 保持在90%，等待实际上传完成
        timer.cancel();
      }
      onItemUpdated(item);
    });
  }

  /// 更新实拍状态
  bool updateCameraShotStatus(List<ImageUploadItem> selectedImages) {
    // 如果没有照片，则不能认证为实拍
    if (selectedImages.isEmpty) {
      debugPrint('🔍 [实拍状态] 无照片，实拍状态: false');
      return false;
    }

    // 检查是否所有照片都来自相机拍摄
    final isCameraShot = selectedImages.every((item) => item.isFromCamera);
    
    debugPrint('🔍 [实拍状态] 实拍状态: $isCameraShot');
    return isCameraShot;
  }

  /// 取消指定图片的上传
  void cancelImageUpload(List<ImageUploadItem> selectedImages, int index) {
    if (index >= 0 && index < selectedImages.length) {
      final item = selectedImages[index];
      if (item.isUploading) {
        item.cancelUpload();
        debugPrint('⚠️ [上传管理] 已取消图片上传: ${item.file.path}');
      }
    }
  }

  /// 重试指定图片的上传
  Future<void> retryImageUpload(
    List<ImageUploadItem> selectedImages,
    int index,
    Function(ImageUploadItem) onItemUpdated,
  ) async {
    if (index >= 0 && index < selectedImages.length) {
      final item = selectedImages[index];
      
      // 检查用户是否已登录
      if (!Services.auth.isLoggedIn) {
        item.errorMessage = '请先登录';
        onItemUpdated(item);
        return;
      }

      final user = Services.auth.currentUser;
      if (user == null) return;

      // 重置状态并重新上传
      item.resetForRetry();
      onItemUpdated(item);
      
      debugPrint('🔄 [上传管理] 重试图片上传: ${item.file.path}');
      await _uploadSingleImage(item, user.id, onItemUpdated);
    }
  }

  /// 检查是否有图片正在上传
  bool hasUploadingImages(List<ImageUploadItem> selectedImages) {
    return selectedImages.any((item) => item.isUploading);
  }

  /// 获取上传状态文本
  String getUploadStatusText(List<ImageUploadItem> selectedImages) {
    final uploadingCount = selectedImages.where((item) => item.isUploading).length;
    if (uploadingCount > 0) {
      return '照片上传中 ($uploadingCount/${selectedImages.length})';
    }
    return '发布钓点';
  }

  /// 关联已上传的图片到钓点
  Future<void> associateUploadedImages(
    String spotId,
    String userId,
    List<ImageUploadItem> uploadedImages,
  ) async {
    debugPrint('🔍 [图片关联] 开始关联已上传的图片');
    debugPrint('🔍 [图片关联] 钓点ID: $spotId');
    debugPrint('🔍 [图片关联] 用户ID: $userId');
    debugPrint('🔍 [图片关联] 图片数量: ${uploadedImages.length}');

    try {
      // 为每张已上传的图片创建数据库记录
      bool saveSuccess = true;
      for (int i = 0; i < uploadedImages.length; i++) {
        final imageItem = uploadedImages[i];

        // 实现图片记录保存功能
        final success = await _savePhotoRecord(
          spotId: spotId,
          userId: userId,
          imageItem: imageItem,
          sortOrder: i,
        );

        if (!success) {
          debugPrint('❌ [图片关联] 照片记录 ${i + 1} 保存失败');
          saveSuccess = false;
        } else {
          debugPrint('✅ [图片关联] 照片记录 ${i + 1} 保存成功');
        }
      }

      if (!saveSuccess) {
        debugPrint('⚠️ [图片关联] 部分照片记录保存失败');
      } else {
        debugPrint('✅ [图片关联] 所有照片记录保存成功');
      }
    } catch (e) {
      debugPrint('❌ [图片关联] 关联失败: $e');
      throw Exception('图片关联失败: $e');
    }
  }

  /// 保存照片记录到数据库
  Future<bool> _savePhotoRecord({
    required String spotId,
    required String userId,
    required ImageUploadItem imageItem,
    required int sortOrder,
  }) async {
    try {
      debugPrint('🔍 [照片记录] 开始保存照片记录到数据库');
      debugPrint('🔍 [照片记录] 钓点ID: $spotId');
      debugPrint('🔍 [照片记录] 用户ID: $userId');
      debugPrint('🔍 [照片记录] 原图URL: ${imageItem.uploadedUrl}');
      debugPrint('🔍 [照片记录] 缩略图URL: ${imageItem.thumbnailUrl}');

      // 获取PocketBase客户端
      final pb = PocketBaseConfig.instance.client;

      // 创建照片记录
      final record = await pb.collection('spot_photos').create(
        body: {
          'spot_id': spotId,
          'user_id': userId,
          'filename': path.basename(imageItem.file.path),
          'url': imageItem.uploadedUrl,
          'thumbnail_url': imageItem.thumbnailUrl ?? imageItem.uploadedUrl,
          'storage_path': imageItem.uploadedUrl, // 使用URL作为存储路径
          'thumbnail_path': imageItem.thumbnailUrl ?? imageItem.uploadedUrl,
          'type': 'normal',
          'description': null,
          'sort_order': sortOrder,
          'file_size': await imageItem.file.length(),
          'mime_type': 'image/jpeg',
          'is_camera_shot': imageItem.isFromCamera, // 添加照片来源信息
          'photo_source': imageItem.isFromCamera ? 'camera' : 'gallery', // 添加照片来源字符串
        },
      );

      debugPrint('🔍 [照片记录] 照片来源: ${imageItem.isFromCamera ? "相机" : "相册"}');
      debugPrint('✅ [照片记录] 保存成功，记录ID: ${record.id}');
      return true;
    } catch (e) {
      debugPrint('❌ [照片记录] 保存失败: $e');
      return false;
    }
  }
}
