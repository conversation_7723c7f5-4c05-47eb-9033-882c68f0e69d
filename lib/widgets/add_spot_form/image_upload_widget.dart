import 'dart:async';
import '../snackbar.dart';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

/// 图片上传项目，包含文件和上传状态
class ImageUploadItem {
  final File file;
  final bool isFromCamera; // 是否来自相机拍摄
  double uploadProgress;
  bool isUploading;
  bool isCompleted;
  bool isCancelled; // 是否已取消
  String? uploadedUrl;
  String? thumbnailUrl;
  String? errorMessage;
  
  // 用于取消上传的控制器
  Completer<void>? _uploadCanceller;

  ImageUploadItem({
    required this.file,
    this.isFromCamera = false,
    this.uploadProgress = 0.0,
    this.isUploading = false,
    this.isCompleted = false,
    this.isCancelled = false,
    this.uploadedUrl,
    this.thumbnailUrl,
    this.errorMessage,
  });

  /// 取消上传
  void cancelUpload() {
    if (isUploading && !isCompleted) {
      isCancelled = true;
      isUploading = false;
      uploadProgress = 0.0;
      errorMessage = '上传已取消';
      _uploadCanceller?.complete();
    }
  }

  /// 设置上传取消控制器
  void setUploadCanceller(Completer<void> canceller) {
    _uploadCanceller = canceller;
  }

  /// 重置状态（用于重试）
  void resetForRetry() {
    isUploading = false;
    isCompleted = false;
    isCancelled = false;
    uploadProgress = 0.0;
    errorMessage = null;
    uploadedUrl = null;
    thumbnailUrl = null;
    _uploadCanceller = null;
  }
}

/// 图片上传组件
///
/// 功能：
/// - 图片选择（相机/相册）
/// - 图片预览
/// - 上传进度显示
/// - 图片删除
class ImageUploadWidget extends StatefulWidget {
  /// 已选择的图片列表
  final List<ImageUploadItem> selectedImages;

  /// 图片添加回调
  final Function(List<ImageUploadItem>) onImagesAdded;

  /// 图片删除回调
  final Function(int) onImageRemoved;

  /// 图片取消上传回调
  final Function(int)? onImageCancelled;

  /// 图片重试上传回调
  final Function(int)? onImageRetry;

  /// 最大图片数量
  final int maxImages;

  /// 是否启用
  final bool enabled;

  const ImageUploadWidget({
    super.key,
    required this.selectedImages,
    required this.onImagesAdded,
    required this.onImageRemoved,
    this.onImageCancelled,
    this.onImageRetry,
    this.maxImages = 9,
    this.enabled = true,
  });

  @override
  State<ImageUploadWidget> createState() => _ImageUploadWidgetState();
}

class _ImageUploadWidgetState extends State<ImageUploadWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              '钓点照片',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.normal,
                color: Colors.black87,
              ),
            ),
            const Spacer(),
            Text(
              '${widget.selectedImages.length}/${widget.maxImages}',
              style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
            ),
          ],
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 80,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount:
                widget.selectedImages.length +
                (widget.selectedImages.length < widget.maxImages ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == widget.selectedImages.length) {
                // 添加照片的方框
                return Container(
                  width: 80,
                  height: 80,
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Colors.grey.shade400,
                      width: 2,
                      style: BorderStyle.solid,
                    ),
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.grey.shade100,
                  ),
                  child: InkWell(
                    onTap:
                        widget.enabled ? () => _showImagePickerOptions(context) : null,
                    borderRadius: BorderRadius.circular(8),
                    child: const Icon(Icons.add, size: 32, color: Colors.grey),
                  ),
                );
              } else {
                // 已选择的照片缩略图
                return Container(
                  width: 80,
                  height: 80,
                  margin: const EdgeInsets.only(right: 8),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: _buildImageThumbnail(widget.selectedImages[index]),
                      ),
                      // 根据不同状态显示不同的操作按钮
                      if (widget.enabled) _buildActionButton(widget.selectedImages[index], index),
                    ],
                  ),
                );
              }
            },
          ),
        ),
      ],
    );
  }

  /// 显示图片选择选项
  Future<void> _showImagePickerOptions(BuildContext context) async {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.camera_alt, color: Colors.blue),
                title: const Text('相机拍照'),
                subtitle: const Text('拍摄新照片（支持实拍认证）'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromCamera(context);
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_library, color: Colors.green),
                title: const Text('从相册选择'),
                subtitle: const Text('选择已有照片'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromGallery(context);
                },
              ),
              ListTile(
                leading: const Icon(Icons.cancel, color: Colors.grey),
                title: const Text('取消'),
                onTap: () => Navigator.pop(context),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 从相机拍照
  Future<void> _pickImageFromCamera(BuildContext context) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image != null) {
        final newItem = ImageUploadItem(
          file: File(image.path),
          isFromCamera: true,
        );

        widget.onImagesAdded([newItem]);

        // 相机拍照成功，不显示提示
      }
    } catch (e) {
      debugPrint('相机拍照失败: $e');
      if (context.mounted) {
        SnackBarService.showError(context, '相机拍照失败，请重试');
      }
    }
  }

  /// 从相册选择图片
  Future<void> _pickImageFromGallery(BuildContext context) async {
    try {
      final ImagePicker picker = ImagePicker();
      final List<XFile> images = await picker.pickMultiImage(
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (images.isNotEmpty) {
        final newItems =
            images
                .map(
                  (xFile) => ImageUploadItem(
                    file: File(xFile.path),
                    isFromCamera: false,
                  ),
                )
                .toList();

        widget.onImagesAdded(newItems);

        // 图片选择成功，不显示提示
      }
    } catch (e) {
      debugPrint('选择图片失败: $e');
      if (context.mounted) {
        SnackBarService.showError(context, '选择图片失败，请重试');
      }
    }
  }

  /// 构建图片缩略图，支持黑白显示和渐进式彩色
  Widget _buildImageThumbnail(ImageUploadItem item) {
    Widget imageWidget =
        kIsWeb
            ? Image.network(
              item.file.path,
              width: 80,
              height: 80,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 80,
                  height: 80,
                  color: Colors.grey[300],
                  child: const Icon(Icons.image),
                );
              },
            )
            : Image.file(item.file, width: 80, height: 80, fit: BoxFit.cover);

    // 如果还没开始上传或正在上传，显示黑白图片
    if (!item.isCompleted) {
      imageWidget = ColorFiltered(
        colorFilter: const ColorFilter.matrix([
          0.2126, 0.7152, 0.0722, 0, 0, // Red channel
          0.2126, 0.7152, 0.0722, 0, 0, // Green channel
          0.2126, 0.7152, 0.0722, 0, 0, // Blue channel
          0, 0, 0, 1, 0, // Alpha channel
        ]),
        child: imageWidget,
      );

      // 如果正在上传，显示渐进式彩色效果
      if (item.isUploading && item.uploadProgress > 0) {
        imageWidget = Stack(
          children: [
            imageWidget, // 黑白背景
            ClipRect(
              child: Align(
                alignment: Alignment.centerLeft,
                widthFactor: item.uploadProgress,
                child:
                    kIsWeb
                        ? Image.network(
                          item.file.path,
                          width: 80,
                          height: 80,
                          fit: BoxFit.cover,
                        )
                        : Image.file(
                          item.file,
                          width: 80,
                          height: 80,
                          fit: BoxFit.cover,
                        ),
              ),
            ),
          ],
        );
      }
    }

    return imageWidget;
  }

  /// 构建操作按钮（取消/删除/重试）
  Widget _buildActionButton(ImageUploadItem item, int index) {
    if (item.isUploading) {
      // 上传中 - 显示取消按钮
      return Positioned(
        top: 4,
        right: 4,
        child: GestureDetector(
          onTap: () => _cancelUpload(index),
          child: Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.9),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: const Icon(
              Icons.close,
              color: Colors.white,
              size: 16,
            ),
          ),
        ),
      );
    } else if (item.isCompleted) {
      // 上传完成 - 显示删除按钮
      return Positioned(
        top: 4,
        right: 4,
        child: GestureDetector(
          onTap: () => widget.onImageRemoved(index),
          child: Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.9),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: const Icon(
              Icons.close,
              color: Colors.white,
              size: 16,
            ),
          ),
        ),
      );
    } else if (item.errorMessage != null || item.isCancelled) {
      // 上传失败或已取消 - 显示重试和删除按钮
      return Positioned(
        top: 4,
        right: 4,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 重试按钮
            GestureDetector(
              onTap: () => _retryUpload(index),
              child: Container(
                width: 24,
                height: 24,
                margin: const EdgeInsets.only(right: 4),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.9),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.refresh,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
            // 删除按钮
            GestureDetector(
              onTap: () => widget.onImageRemoved(index),
              child: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.9),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
          ],
        ),
      );
    }
    
    return const SizedBox.shrink();
  }


  /// 取消上传
  void _cancelUpload(int index) {
    if (widget.onImageCancelled != null) {
      widget.onImageCancelled!(index);
    }
  }

  /// 重试上传
  void _retryUpload(int index) {
    if (widget.onImageRetry != null) {
      widget.onImageRetry!(index);
    }
  }
}
