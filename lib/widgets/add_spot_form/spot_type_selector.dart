import 'package:flutter/material.dart';
import '../../models/spot_type.dart';
import '../../theme/unified_theme.dart';

/// 钓点类型选择器组件
///
/// 功能：
/// - 钓点类型选择
/// - 可视化选择界面
/// - 状态管理
class SpotTypeSelector extends StatelessWidget {
  /// 当前选中的钓点类型
  final SpotType selectedSpotType;

  /// 钓点类型变更回调
  final Function(SpotType) onChanged;

  /// 是否启用
  final bool enabled;

  const SpotTypeSelector({
    super.key,
    required this.selectedSpotType,
    required this.onChanged,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('钓点类型', style: AppTheme.headlineSmall),
        const SizedBox(height: AppTheme.spacingS),
        Align(
          alignment: Alignment.centerLeft,
          child: Wrap(
            alignment: WrapAlignment.start,
            spacing: AppTheme.spacingXS,
            runSpacing: AppTheme.spacingXS,
            children:
                SpotType.values.map((type) {
                  final isSelected = selectedSpotType == type;

                  return GestureDetector(
                    onTap: enabled ? () => onChanged(type) : null,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.spacingS,
                        vertical: AppTheme.spacingXS,
                      ),
                      decoration: AppTheme.selectorDecoration(
                        isSelected: isSelected,
                        selectedColor: _getSelectedColor(type),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            type.emoji,
                            style: const TextStyle(fontSize: 14),
                          ),
                          const SizedBox(width: AppTheme.spacingXS),
                          Text(
                            type.displayName,
                            style: AppTheme.labelMedium.copyWith(
                              fontSize: 13,
                              color:
                                  isSelected
                                      ? _getSelectedColor(type)
                                      : AppTheme.textPrimaryColor,
                              fontWeight:
                                  isSelected
                                      ? FontWeight.w600
                                      : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
          ),
        ),
        // 显示选中类型的描述
        if (selectedSpotType != SpotType.wildFishing) ...[
          const SizedBox(height: AppTheme.spacingXS),
          Text(
            selectedSpotType.description,
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.textSecondaryColor,
              fontSize: 12,
            ),
          ),
        ],
      ],
    );
  }

  /// 根据钓点类型获取选中时的颜色
  Color _getSelectedColor(SpotType type) {
    switch (type) {
      case SpotType.wildFishing:
        return AppTheme.successColor; // 绿色
      case SpotType.seaFishing:
        return AppTheme.infoColor; // 蓝色
      case SpotType.lureFishing:
        return Colors.purple; // 紫色
      case SpotType.paidFishPond:
        return AppTheme.warningColor; // 橙色
    }
  }
}
