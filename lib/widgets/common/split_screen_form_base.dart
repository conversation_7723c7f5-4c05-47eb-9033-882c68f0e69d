import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:latlong2/latlong.dart';

import '../snackbar.dart';
import '../../services/service_locator.dart';

/// 分屏表单基础组件
///
/// 提供通用的分屏表单结构和交互逻辑，支持泛型以处理不同类型的数据模型
abstract class SplitScreenFormBase<T> extends StatefulWidget {
  /// 位置
  final LatLng location;

  /// 位置更新回调
  final Function(LatLng) onLocationChanged;

  /// 关闭回调
  final VoidCallback onClose;

  /// 成功添加回调
  final Function(T) onSpotAdded;

  /// 建议的名称
  final String? suggestedName;

  const SplitScreenFormBase({
    super.key,
    required this.location,
    required this.onLocationChanged,
    required this.onClose,
    required this.onSpotAdded,
    this.suggestedName,
  });
}

/// 分屏表单基础状态类
abstract class SplitScreenFormBaseState<T, W extends SplitScreenFormBase<T>>
    extends State<W> {
  // 通用状态变量
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  DraggableScrollableController? dragController;
  ScrollController? currentScrollController;

  // UI状态
  bool isSubmitting = false;

  // 表单键用于滚动定位
  final Map<String, GlobalKey> formKeys = {};

  @override
  void initState() {
    super.initState();
    dragController = DraggableScrollableController();
    initializeFormKeys();
    onInitialize();
  }

  @override
  void dispose() {
    dragController?.dispose();
    onDispose();
    super.dispose();
  }

  /// 子类初始化方法
  void onInitialize() {}

  /// 子类销毁方法
  void onDispose() {}

  /// 初始化表单键
  void initializeFormKeys() {
    final keys = getFormKeyNames();
    for (final keyName in keys) {
      formKeys[keyName] = GlobalKey();
    }
  }

  /// 获取表单键名称列表（子类实现）
  List<String> getFormKeyNames();

  /// 获取表单标题（子类实现）
  String getFormTitle();

  /// 获取提交按钮文本（子类实现）
  String getSubmitButtonText();

  /// 获取提交按钮图标（子类实现）
  Widget getSubmitButtonIcon();

  /// 构建表单内容（子类实现）
  List<Widget> buildFormContent();

  /// 验证表单（子类实现）
  bool validateForm();

  /// 处理提交（子类实现）
  Future<void> handleSubmit();

  /// 检查用户登录状态
  Future<bool> checkUserLogin() async {
    if (!Services.auth.isLoggedIn) {
      final result = await Navigator.pushNamed(context, '/login');
      if (result == true && Services.auth.isLoggedIn) {
        return true;
      }
      return false;
    }
    return true;
  }

  /// 滚动到第一个错误字段
  void scrollToFirstError() {
    if (currentScrollController != null && formKeys.isNotEmpty) {
      final firstKey = formKeys.values.first;
      scrollToWidget(firstKey);
    }
  }

  /// 滚动到指定的组件
  void scrollToWidget(GlobalKey key) {
    if (currentScrollController == null) return;

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final context = key.currentContext;
      if (context != null) {
        try {
          await Scrollable.ensureVisible(
            context,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
            alignment: 0.1,
          );
          debugPrint('🔍 [滚动] 滚动到目标组件');
        } catch (e) {
          debugPrint('❌ [滚动] 滚动失败: $e');
        }
      }
    });
  }

  /// 构建表单卡片
  Widget buildFormCard({
    Key? key,
    required Widget child,
    bool showBottomBorder = true,
  }) {
    return Container(
      key: key,
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        border:
            showBottomBorder
                ? Border(
                  bottom: BorderSide(color: Colors.grey.shade200, width: 0.5),
                )
                : null,
      ),
      child: child,
    );
  }

  /// 构建坐标显示组件
  Widget buildCoordinateDisplay() {
    return buildFormCard(
      child: Row(
        children: [
          Icon(Icons.location_on, size: 20, color: Colors.blue.shade600),
          const SizedBox(width: 8),
          const Text(
            '坐标位置',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '${widget.location.latitude.toStringAsFixed(6)}, ${widget.location.longitude.toStringAsFixed(6)}',
              style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  /// 处理提交的通用逻辑
  Future<void> onSubmit() async {
    // 检查登录状态
    if (!await checkUserLogin()) {
      return;
    }

    // 验证表单
    if (!validateForm()) {
      return;
    }

    setState(() {
      isSubmitting = true;
    });

    try {
      await handleSubmit();
    } catch (e) {
      if (mounted) {
        SnackBarService.showError(context, '提交失败: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      controller: dragController,
      initialChildSize: 0.5,
      minChildSize: 0.3,
      maxChildSize: 0.85,
      snap: true,
      snapSizes: const [0.3, 0.5, 0.85],
      builder: (context, scrollController) {
        currentScrollController = scrollController;

        // 调试信息
        debugPrint('🔧 [DraggableSheet] Builder called');
        debugPrint('🔧 [DraggableSheet] ScrollController: $scrollController');
        debugPrint('🔧 [DraggableSheet] initialChildSize: 0.5');
        debugPrint('🔧 [DraggableSheet] minChildSize: 0.3');
        debugPrint('🔧 [DraggableSheet] maxChildSize: 0.85');

        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Colors.white, Colors.grey.shade50],
            ),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                blurRadius: 32,
                offset: const Offset(0, -8),
                spreadRadius: 0,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
            child:
                isSubmitting
                    ? const Center(child: CircularProgressIndicator())
                    : Form(
                      key: formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 拖拽指示器
                          _buildDragIndicator(),

                          // 标题栏
                          _buildHeader(),

                          // 表单内容区域
                          Expanded(
                            child: Container(
                              color: Colors.grey.shade50,
                              child: SingleChildScrollView(
                                controller: scrollController,
                                physics: const BouncingScrollPhysics(),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // 坐标信息
                                    buildCoordinateDisplay(),

                                    // 子类自定义内容
                                    ...buildFormContent(),

                                    // 提交按钮区域
                                    _buildSubmitSection(),

                                    // 底部安全区域
                                    const SizedBox(height: 24),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
            );
          },
        );
      },
    );
  }

  /// 构建拖拽指示器
  Widget _buildDragIndicator() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Center(
        child: Container(
          margin: const EdgeInsets.only(top: 8, bottom: 0),
          width: 48,
          height: 5,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.grey.shade300, Colors.grey.shade400],
            ),
            borderRadius: BorderRadius.circular(3),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 1),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建标题栏
  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
      child: Row(
        children: [
          Text(
            getFormTitle(),
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.close, color: Colors.grey, size: 24),
            onPressed: widget.onClose,
          ),
        ],
      ),
    );
  }

  /// 构建提交区域
  Widget _buildSubmitSection() {
    return Container(
      color: Colors.grey.shade50,
      padding: const EdgeInsets.all(20),
      child: Container(
        width: double.infinity,
        height: 56,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors:
                isSubmitting
                    ? [Colors.grey.shade300, Colors.grey.shade400]
                    : [Colors.green.shade400, Colors.green.shade600],
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow:
              isSubmitting
                  ? []
                  : [
                    BoxShadow(
                      color: Colors.green.withValues(alpha: 0.4),
                      blurRadius: 16,
                      offset: const Offset(0, 4),
                    ),
                  ],
        ),
        child: ElevatedButton(
          onPressed: isSubmitting ? null : onSubmit,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.transparent,
            shadowColor: Colors.transparent,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              getSubmitButtonIcon(),
              const SizedBox(width: 8),
              Text(
                getSubmitButtonText(),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 0.5,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 优化的拖拽指示器组件
class _DragIndicator extends StatefulWidget {
  final VoidCallback? onDragStart;
  final ValueChanged<double>? onDragUpdate;
  final VoidCallback? onDragEnd;

  const _DragIndicator({this.onDragStart, this.onDragUpdate, this.onDragEnd});

  @override
  State<_DragIndicator> createState() => _DragIndicatorState();
}

class _DragIndicatorState extends State<_DragIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;

  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _colorAnimation = ColorTween(
      begin: Colors.grey.shade400,
      end: Colors.grey.shade600,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    setState(() {
      _isPressed = true;
    });
    _animationController.forward();
    widget.onDragStart?.call();
  }

  void _handleTapUp(TapUpDetails details) {
    setState(() {
      _isPressed = false;
    });
    _animationController.reverse();
    widget.onDragEnd?.call();
  }

  void _handleTapCancel() {
    setState(() {
      _isPressed = false;
    });
    _animationController.reverse();
  }

  void _handlePanStart(DragStartDetails details) {
    setState(() {
      _isPressed = true;
    });
    _animationController.forward();
    widget.onDragStart?.call();
  }

  void _handlePanUpdate(DragUpdateDetails details) {
    widget.onDragUpdate?.call(details.delta.dy);
  }

  void _handlePanEnd(DragEndDetails details) {
    setState(() {
      _isPressed = false;
    });
    _animationController.reverse();
    widget.onDragEnd?.call();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      onPanStart: _handlePanStart,
      onPanUpdate: _handlePanUpdate,
      onPanEnd: _handlePanEnd,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
        child: Center(
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: Container(
                  width: 60,
                  height: 6,
                  decoration: BoxDecoration(
                    color: _colorAnimation.value,
                    borderRadius: BorderRadius.circular(3),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(
                          alpha: _isPressed ? 0.2 : 0.1,
                        ),
                        blurRadius: _isPressed ? 8 : 4,
                        offset: Offset(0, _isPressed ? 2 : 1),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
