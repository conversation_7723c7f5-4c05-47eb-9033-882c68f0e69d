import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';
import '../services/service_locator.dart';
import '../services/location_service.dart';
import '../test_activity_marker.dart';

/// 开发者调试菜单
/// 仅在开发模式下显示，提供快速访问测试页面和开发工具的入口
class DevMenu extends StatelessWidget {
  const DevMenu({super.key});

  @override
  Widget build(BuildContext context) {
    // 如果不是开发模式，不显示菜单
    if (!AppConfig.instance.enableDeveloperTools) {
      return const SizedBox.shrink();
    }

    return PopupMenuButton<String>(
      icon: const Icon(FontAwesomeIcons.bug, color: Colors.orange, size: 20),
      tooltip: '开发者工具',
      onSelected: (value) => _handleMenuSelection(context, value),
      itemBuilder:
          (context) => [
            const PopupMenuItem(
              value: 'mock_location',
              child: ListTile(
                leading: Icon(FontAwesomeIcons.locationDot, size: 16),
                title: Text('模拟位置'),
                subtitle: Text('设置调试位置'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'cache_stats',
              child: ListTile(
                leading: Icon(FontAwesomeIcons.chartBar, size: 16),
                title: Text('缓存统计'),
                subtitle: Text('查看瓦片缓存统计'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'clear_cache',
              child: ListTile(
                leading: Icon(FontAwesomeIcons.trash, size: 16),
                title: Text('清除缓存'),
                subtitle: Text('清除所有本地缓存'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'reload_data',
              child: ListTile(
                leading: Icon(FontAwesomeIcons.arrowsRotate, size: 16),
                title: Text('重新加载数据'),
                subtitle: Text('刷新所有数据'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'r2_upload_test',
              child: ListTile(
                leading: Icon(FontAwesomeIcons.cloudArrowUp, size: 16),
                title: Text('R2上传测试'),
                subtitle: Text('客户端签名上传测试'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'marker_test',
              child: ListTile(
                leading: Icon(FontAwesomeIcons.mapPin, size: 16),
                title: Text('标记测试'),
                subtitle: Text('测试钓点标记效果'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'activity_marker_test',
              child: ListTile(
                leading: Icon(FontAwesomeIcons.fish, size: 16),
                title: Text('活动标记测试'),
                subtitle: Text('测试活动标记组件'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'visibility_test',
              child: ListTile(
                leading: Icon(FontAwesomeIcons.eye, size: 16),
                title: Text('可见性测试'),
                subtitle: Text('测试钓点可见性功能'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'app_info',
              child: ListTile(
                leading: Icon(FontAwesomeIcons.circleInfo, size: 16),
                title: Text('应用信息'),
                subtitle: Text('查看版本和配置'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
    );
  }

  /// 处理菜单选择
  void _handleMenuSelection(BuildContext context, String value) {
    switch (value) {
      case 'mock_location':
        _showMockLocationDialog(context);
        break;
      case 'cache_stats':
        _showCacheStats(context);
        break;
      case 'clear_cache':
        _clearCache(context);
        break;
      case 'reload_data':
        _reloadData(context);
        break;
      case 'app_info':
        _showAppInfo(context);
        break;
      case 'activity_marker_test':
        _showActivityMarkerTest(context);
        break;
    }
  }

  /// 清除缓存
  void _clearCache(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('清除缓存'),
            content: const Text('确定要清除所有本地缓存吗？这将删除用户数据、钓点数据和地图瓦片缓存。'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  await _performClearCache(context);
                },
                child: const Text('确定'),
              ),
            ],
          ),
    );
  }

  /// 执行清除缓存操作
  Future<void> _performClearCache(BuildContext context) async {
    try {
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => const AlertDialog(
              content: Row(
                children: [
                  CircularProgressIndicator(),
                  SizedBox(width: 16),
                  Text('正在清除缓存...'),
                ],
              ),
            ),
      );

      // 清除各种缓存
      await Future.wait([
        // 清除瓦片缓存
        Services.cache.clearCache(),
        // 清除用户缓存
        Future.microtask(() => Services.user.clearCache()),
        // 清除钓点缓存
        Future.microtask(() => Services.fishingSpot.clearCache()),
        // 清除SharedPreferences中的其他缓存数据
        _clearSharedPreferencesCache(),
      ]);

      // 关闭加载对话框
      if (context.mounted) {
        Navigator.pop(context);

        // 显示成功消息
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('缓存清除完成')));
      }
    } catch (e) {
      // 关闭加载对话框
      if (context.mounted) {
        Navigator.pop(context);

        // 显示错误消息
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('清除缓存失败: $e')));
      }
    }
  }

  /// 重新加载数据
  void _reloadData(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('重新加载数据'),
            content: const Text('确定要重新加载所有数据吗？这将从服务器获取最新数据。'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  await _performReloadData(context);
                },
                child: const Text('确定'),
              ),
            ],
          ),
    );
  }

  /// 执行重新加载数据操作
  Future<void> _performReloadData(BuildContext context) async {
    try {
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => const AlertDialog(
              content: Row(
                children: [
                  CircularProgressIndicator(),
                  SizedBox(width: 16),
                  Text('正在重新加载数据...'),
                ],
              ),
            ),
      );

      // 重新加载数据
      await Future.wait([
        Services.user.getAllUsers(),
        // 获取附近钓点而不是所有钓点
        () async {
          final userLocation = Services.location.getCurrentLocation();
          return await Services.fishingSpot.getSpotsInBounds(
            minLat: userLocation.latitude - 0.05,
            maxLat: userLocation.latitude + 0.05,
            minLng: userLocation.longitude - 0.05,
            maxLng: userLocation.longitude + 0.05,
            limit: 20,
          );
        }(),
      ]);

      // 关闭加载对话框
      if (context.mounted) {
        Navigator.pop(context);

        // 显示成功消息
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('数据重新加载完成')));
      }
    } catch (e) {
      // 关闭加载对话框
      if (context.mounted) {
        Navigator.pop(context);

        // 显示错误消息
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('重新加载数据失败: $e')));
      }
    }
  }

  /// 清除SharedPreferences缓存
  Future<void> _clearSharedPreferencesCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();

      // 保留重要的配置项，只清除缓存相关的键
      final keysToRemove =
          keys
              .where(
                (key) =>
                    key.contains('cache') ||
                    key.contains('temp') ||
                    key.startsWith('users_cache') ||
                    key.startsWith('fishing_spots_cache') ||
                    key.startsWith('tile_cache_'),
              )
              .toList();

      for (final key in keysToRemove) {
        await prefs.remove(key);
      }

      debugPrint('清除了 ${keysToRemove.length} 个SharedPreferences缓存项');
    } catch (e) {
      debugPrint('清除SharedPreferences缓存失败: $e');
    }
  }

  /// 显示应用信息
  void _showAppInfo(BuildContext context) {
    final config = AppConfig.instance;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('应用信息'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('应用版本: ${config.appVersion}'),
                Text('构建模式: ${config.buildMode}'),
                Text('平台: ${config.platformInfo}'),
                const SizedBox(height: 16),
                const Text(
                  '开发功能状态:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text('开发模式: ${config.isDevelopmentMode ? '启用' : '禁用'}'),
                Text(
                  'PocketBase测试: ${config.enablePocketBaseTestPage ? '启用' : '禁用'}',
                ),
                Text('网络日志: ${config.enableNetworkLogging ? '启用' : '禁用'}'),
                Text('缓存调试: ${config.enableCacheDebugging ? '启用' : '禁用'}'),
                Text(
                  '性能监控: ${config.enablePerformanceMonitoring ? '启用' : '禁用'}',
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('确定'),
              ),
            ],
          ),
    );
  }

  /// 显示缓存统计信息
  void _showCacheStats(BuildContext context) async {
    try {
      // 获取缓存统计信息
      final cacheStats = await Services.cache.getCacheStats();

      if (!context.mounted) return;

      showDialog(
        context: context,
        builder:
            (context) => AlertDialog(
              title: const Text('瓦片缓存统计'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('初始化状态: ${cacheStats['initialized'] ? '已初始化' : '未初始化'}'),
                  if (cacheStats['initialized'] == true) ...[
                    const SizedBox(height: 8),
                    Text('平台: ${cacheStats['platform']}'),
                    Text('缓存条目数: ${cacheStats['entryCount']}'),
                    Text('总大小: ${_formatBytes(cacheStats['totalSize'])}'),
                    const SizedBox(height: 8),
                    const Text(
                      '性能统计:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text('缓存命中: ${cacheStats['cacheHits']}'),
                    Text('缓存未命中: ${cacheStats['cacheMisses']}'),
                    Text('下载错误: ${cacheStats['downloadErrors']}'),
                    Text('命中率: ${cacheStats['hitRate']}%'),
                    Text('总请求数: ${cacheStats['totalRequests']}'),
                  ],
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Services.cache.resetStats();
                    Navigator.pop(context);
                    ScaffoldMessenger.of(
                      context,
                    ).showSnackBar(const SnackBar(content: Text('统计信息已重置')));
                  },
                  child: const Text('重置统计'),
                ),
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('关闭'),
                ),
              ],
            ),
      );
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('获取缓存统计失败: $e')));
      }
    }
  }

  /// 格式化字节大小
  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// 显示模拟位置选择对话框
  void _showMockLocationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('选择模拟位置'),
            content: SizedBox(
              width: double.maxFinite,
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: LocationService.mockLocations.length,
                itemBuilder: (context, index) {
                  final entry = LocationService.mockLocations.entries.elementAt(
                    index,
                  );
                  final name = entry.key;
                  final location = entry.value;

                  return ListTile(
                    leading: const Icon(Icons.location_on),
                    title: Text(name),
                    subtitle: Text(
                      '${location.latitude.toStringAsFixed(4)}, ${location.longitude.toStringAsFixed(4)}',
                    ),
                    onTap: () async {
                      Navigator.pop(context);
                      await LocationService().setMockLocation(location);

                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('已设置模拟位置: $name'),
                            duration: const Duration(seconds: 2),
                          ),
                        );
                      }
                    },
                  );
                },
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('取消'),
              ),
            ],
          ),
    );
  }

  /// 显示活动标记测试页面
  void _showActivityMarkerTest(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const TestActivityMarkerPage()),
    );
  }
}
