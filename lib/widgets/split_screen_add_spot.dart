import 'dart:async';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:uuid/uuid.dart';

import '../models/fishing_spot.dart';
import '../models/spot_visibility.dart';
import '../models/spot_type.dart';
import '../theme/unified_theme.dart';
import '../services/location_verification_service.dart';
import '../services/service_locator.dart';
import '../config/pocketbase_config.dart';
import 'package:path/path.dart' as path;

// 导入新的模块化组件
import 'add_spot_form/spot_name_input.dart';
import 'add_spot_form/spot_description_input.dart';
import 'add_spot_form/water_level_selector.dart';
import 'add_spot_form/bait_selector.dart';
import 'add_spot_form/fish_type_selector.dart';
import 'add_spot_form/spot_type_selector.dart';
import 'add_spot_form/image_upload_widget.dart';
import 'add_spot_form/image_upload_manager.dart';

import 'snackbar.dart';

// 导入基础组件和混入
import 'common/split_screen_form_base.dart';
import 'mixins/form_validation_mixin.dart';
import 'mixins/scroll_behavior_mixin.dart';

/// 分屏添加钓点组件（重构版本）
class SplitScreenAddSpot extends SplitScreenFormBase<FishingSpot> {
  const SplitScreenAddSpot({
    super.key,
    required super.location,
    required super.onLocationChanged,
    required super.onClose,
    required super.onSpotAdded,
    super.suggestedName,
  });

  @override
  State<SplitScreenAddSpot> createState() => _SplitScreenAddSpotState();
}

class _SplitScreenAddSpotState
    extends SplitScreenFormBaseState<FishingSpot, SplitScreenAddSpot>
    with FormValidationMixin, ScrollBehaviorMixin {
  // 钓点特有的管理器和控制器
  final _imageUploadManager = ImageUploadManager();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  // 钓点特有的表单状态
  String _selectedWaterLevel = '正常';
  String _selectedBait = '商品饵';
  String _selectedFishType = 'carp'; // 默认鲤鱼
  SpotType _selectedSpotType = SpotType.wildFishing; // 默认野钓
  final List<ImageUploadItem> _selectedImages = [];

  // 位置验证状态
  Position? _publishLocation;
  bool _isOnSite = false;
  StreamSubscription<Position>? _locationSubscription;

  @override
  ScrollController? get currentScrollController =>
      super.currentScrollController;

  @override
  Map<String, GlobalKey> get formKeys => super.formKeys;

  @override
  void onInitialize() {
    // 如果有建议的钓点名称，设置到名称字段中
    if (widget.suggestedName != null &&
        widget.suggestedName!.trim().isNotEmpty) {
      _nameController.text = widget.suggestedName!;
    }
  }

  @override
  void onDispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _locationSubscription?.cancel();
  }

  @override
  List<String> getFormKeyNames() {
    return ['nameInput', 'imageUpload', 'descriptionInput'];
  }

  @override
  String getFormTitle() {
    return '添加钓点';
  }

  @override
  String getSubmitButtonText() {
    if (isSubmitting) {
      return '发布中...';
    }
    if (_imageUploadManager.hasUploadingImages(_selectedImages)) {
      return '等待图片上传';
    }
    return '发布钓点';
  }

  @override
  Widget getSubmitButtonIcon() {
    if (isSubmitting ||
        _imageUploadManager.hasUploadingImages(_selectedImages)) {
      return const SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2.5,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      );
    }

    // 根据实地验证状态显示不同图标
    IconData iconData = _isOnSite ? Icons.location_on : Icons.publish_rounded;

    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Icon(iconData, color: Colors.white, size: 18),
    );
  }

  @override
  bool validateForm() {
    // 验证图片
    if (!validateImages(_selectedImages, errorMessage: '请添加钓点照片')) {
      scrollToFormField('imageUpload');
      return false;
    }

    // 验证图片上传状态
    if (!validateImageUploading(
      _selectedImages,
      _imageUploadManager.hasUploadingImages,
      errorMessage: '请等待图片上传完成',
    )) {
      scrollToFormField('imageUpload');
      return false;
    }

    // 验证钓点名称
    if (!validateTextNotEmpty(_nameController.text, errorMessage: '请输入钓点名称')) {
      scrollToFormField('nameInput');
      return false;
    }

    // 验证钓点描述
    if (!validateTextMinLength(
      _descriptionController.text,
      15,
      errorMessage: '钓点描述至少需要15个字符',
    )) {
      scrollToFormField('descriptionInput');
      return false;
    }

    // 验证表单字段
    if (!validateFormFields(formKey)) {
      scrollToFirstError();
      return false;
    }

    return true;
  }

  @override
  Future<void> handleSubmit() async {
    final user = Services.auth.currentUser;

    // 创建钓点对象
    String description = _descriptionController.text.trim();

    // 将水位和饵料信息添加到描述中
    List<String> additionalInfo = [];
    if (_selectedWaterLevel != '正常') {
      additionalInfo.add('水位：$_selectedWaterLevel');
    }
    if (_selectedBait != '商品饵') {
      additionalInfo.add('饵料：$_selectedBait');
    }

    if (additionalInfo.isNotEmpty) {
      if (description.isNotEmpty) {
        description += '\n\n';
      }
      description += additionalInfo.join('，');
    }

    final clientGeneratedId = const Uuid().v4();

    final spot = FishingSpot(
      id: clientGeneratedId,
      name: _nameController.text.trim(),
      description: description,
      location: {
        'lat': widget.location.latitude,
        'lon': widget.location.longitude,
      },
      userId: user?.id ?? '',
      userName: user?.username,
      spotType: _selectedSpotType.value,
      fishTypes: _selectedFishType,
      spotEmoji: '🎣',
      fishEmoji: '🐟',
      visibility: SpotVisibility.public,
      visibilityConditions: null,
      visibilityUpdatedAt: DateTime.now(),
      created: DateTime.now(),
      updated: DateTime.now(),
    );

    // 添加钓点
    final addedSpot = await Services.fishingSpot.addSpot(spot);

    if (addedSpot != null) {
      // 异步关联已上传的图片到钓点
      if (_selectedImages.isNotEmpty) {
        final uploadedImages =
            _selectedImages
                .where(
                  (img) =>
                      img.uploadedUrl != null && img.uploadedUrl!.isNotEmpty,
                )
                .toList();

        if (uploadedImages.isNotEmpty) {
          _associateUploadedImagesAsync(addedSpot, user, uploadedImages);
        }
      }

      // 调用成功回调
      widget.onSpotAdded(addedSpot);

      // 显示成功提示
      showSuccessMessage('钓点发布成功！');

      // 清空表单内容
      _clearForm();
    } else {
      throw Exception('添加钓点失败');
    }
  }

  @override
  List<Widget> buildFormContent() {
    return [
      // 钓点名称
      buildFormCard(
        key: formKeys['nameInput'],
        showBottomBorder: false,
        child: SpotNameInput(
          controller: _nameController,
          location: widget.location,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入钓点名称';
            }
            return null;
          },
        ),
      ),

      // 图片上传
      buildFormCard(
        key: formKeys['imageUpload'],
        showBottomBorder: false,
        child: ImageUploadWidget(
          selectedImages: _selectedImages,
          onImagesAdded: _handleImagesAdded,
          onImageRemoved: _handleImageRemoved,
          onImageCancelled: _handleImageCancelled,
          onImageRetry: _handleImageRetry,
        ),
      ),

      // 钓点描述
      buildFormCard(
        key: formKeys['descriptionInput'],
        child: SpotDescriptionInput(
          controller: _descriptionController,
          labelText: '钓点描述',
          hintText: '描述钓点的特色、环境、注意事项等...',
          showCharacterCount: true,
          minLength: 15,
          requireInput: true,
        ),
      ),

      // 水位、饵料和鱼种选择
      buildFormCard(
        child: Column(
          children: [
            WaterLevelSelector(
              selectedWaterLevel: _selectedWaterLevel,
              onChanged: (value) => setState(() => _selectedWaterLevel = value),
            ),
            const SizedBox(height: AppTheme.spacingL),
            SpotTypeSelector(
              selectedSpotType: _selectedSpotType,
              onChanged: (value) => setState(() => _selectedSpotType = value),
            ),
            const SizedBox(height: AppTheme.spacingL),
            BaitSelector(
              selectedBait: _selectedBait,
              onChanged: (value) => setState(() => _selectedBait = value),
            ),
            const SizedBox(height: AppTheme.spacingL),
            FishTypeSelector(
              selectedFishType: _selectedFishType,
              onChanged: (value) => setState(() => _selectedFishType = value),
            ),
          ],
        ),
      ),
    ];
  }

  @override
  void didUpdateWidget(SplitScreenAddSpot oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 当建议名称更新时，如果输入框为空，则填充新的建议名称
    if (widget.suggestedName != oldWidget.suggestedName &&
        widget.suggestedName != null &&
        widget.suggestedName!.trim().isNotEmpty &&
        _nameController.text.trim().isEmpty) {
      _nameController.text = widget.suggestedName!;
    }

    // 当位置发生变化时，重新计算实地验证状态
    if (widget.location != oldWidget.location) {
      _updateOnSiteVerificationStatus();
    }
  }

  /// 更新实地验证状态
  void _updateOnSiteVerificationStatus() {
    if (_publishLocation != null) {
      _isOnSite = LocationVerificationService.isOnSite(
        widget.location,
        _publishLocation!,
      );

      if (mounted) {
        setState(() {});
      }
    }
  }

  // 删除未使用的方法

  /// 处理图片添加
  void _handleImagesAdded(List<ImageUploadItem> newImages) {
    setState(() {
      _selectedImages.addAll(newImages);
    });

    // 开始上传图片
    _imageUploadManager.uploadSelectedImages(newImages, (item) {
      setState(() {
        // 图片状态已更新
      });
    });
  }

  /// 处理图片删除
  void _handleImageRemoved(int index) {
    if (index >= 0 && index < _selectedImages.length) {
      setState(() {
        _selectedImages.removeAt(index);
      });
    }
  }

  /// 处理图片取消上传
  void _handleImageCancelled(int index) {
    _imageUploadManager.cancelImageUpload(_selectedImages, index);
    setState(() {
      // 触发UI更新
    });
  }

  /// 处理图片重试上传
  void _handleImageRetry(int index) {
    _imageUploadManager.retryImageUpload(_selectedImages, index, (item) {
      setState(() {
        // 图片状态已更新
      });
    });
  }

  // 删除未使用的方法

  /// 清空表单内容和状态
  void _clearForm() {
    // 清空文本输入框
    _nameController.clear();
    _descriptionController.clear();

    // 重置选择项为默认值
    setState(() {
      _selectedWaterLevel = '正常';
      _selectedBait = '商品饵';
      _selectedSpotType = SpotType.wildFishing;

      // 清空图片列表
      _selectedImages.clear();
    });

    debugPrint('✅ [表单清空] 表单内容已清空');
  }

  /// 异步关联图片（后台处理，不阻塞UI）
  void _associateUploadedImagesAsync(
    FishingSpot spot,
    dynamic user,
    List<ImageUploadItem> uploadedImages,
  ) {
    // 在后台异步处理，不阻塞UI
    Future.microtask(() async {
      try {
        await _associateUploadedImages(spot, user, uploadedImages);

        // 关联完成后显示成功提示
        if (mounted) {
          SnackBarService.showSuccess(context, '图片关联完成');
        }
      } catch (e) {
        debugPrint('❌ [异步图片关联] 失败: $e');
        if (mounted) {
          SnackBarService.showWarning(context, '图片关联失败: $e');
        }
      }
    });
  }

  /// 关联已上传的图片到钓点（并行处理）
  Future<void> _associateUploadedImages(
    FishingSpot spot,
    dynamic user,
    List<ImageUploadItem> uploadedImages,
  ) async {
    try {
      // 并行处理所有图片记录创建
      final futures =
          uploadedImages.asMap().entries.map((entry) {
            final index = entry.key;
            final imageItem = entry.value;

            return _savePhotoRecord(
              spotId: spot.id,
              userId: user.id,
              imageItem: imageItem,
              sortOrder: index,
            );
          }).toList();

      // 等待所有图片记录创建完成
      final results = await Future.wait(futures);

      // 检查结果
      final successCount = results.where((success) => success).length;
      final failureCount = results.length - successCount;

      if (failureCount > 0) {
        debugPrint('⚠️ [图片关联] $successCount 张照片记录保存成功，$failureCount 张失败');
      } else {
        debugPrint('✅ [图片关联] 所有 $successCount 张照片记录保存成功');
      }
    } catch (e) {
      debugPrint('❌ [图片关联] 关联失败: $e');
      // 图片关联失败不影响钓点发布，只显示警告
      if (mounted) {
        SnackBarService.showWarning(context, '钓点发布成功，但图片关联失败: $e');
      }
    }
  }

  /// 保存照片记录到数据库
  Future<bool> _savePhotoRecord({
    required String spotId,
    required String userId,
    required ImageUploadItem imageItem,
    required int sortOrder,
  }) async {
    try {
      // 获取PocketBase客户端
      final pb = PocketBaseConfig.instance.client;

      // 创建照片记录
      final record = await pb
          .collection('spot_photos')
          .create(
            body: {
              'spot_id': spotId,
              'user_id': userId,
              'filename': path.basename(imageItem.file.path),
              'url': imageItem.uploadedUrl,
              'thumbnail_url': imageItem.thumbnailUrl ?? imageItem.uploadedUrl,
              'storage_path': imageItem.uploadedUrl,
              'thumbnail_path': imageItem.thumbnailUrl ?? imageItem.uploadedUrl,
              'type': 'normal',
              'description': null,
              'sort_order': sortOrder,
              'file_size': await imageItem.file.length(),
              'mime_type': 'image/jpeg',
              'is_camera_shot': imageItem.isFromCamera,
              'photo_source': imageItem.isFromCamera ? 'camera' : 'gallery',
            },
          );

      debugPrint('✅ [照片记录] 保存成功，记录ID: ${record.id}');
      return true;
    } catch (e) {
      debugPrint('❌ [照片记录] 保存失败: $e');
      return false;
    }
  }
}
