import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:fishing_app/services/fishing_activity_service.dart';
import 'package:fishing_app/models/fishing_activity.dart';

// Mock classes
class MockFishingActivityService extends Mock implements FishingActivityService {}

void main() {
  group('参与者管理功能测试', () {
    late MockFishingActivityService mockService;
    
    setUp(() {
      mockService = MockFishingActivityService();
    });

    test('活动创建者可以踢出参与者', () async {
      // Arrange
      const activityId = 'test_activity_id';
      const participantUserId = 'participant_user_id';
      const creatorUserId = 'creator_user_id';
      
      final mockActivity = FishingActivity(
        id: activityId,
        title: '测试活动',
        description: '测试描述',
        location: {'lat': 39.9042, 'lon': 116.4074},
        startTime: DateTime.now().add(const Duration(hours: 1)),
        duration: 2.0,
        maxParticipants: 10,
        currentParticipants: 2,
        creatorId: creatorUserId,
        creatorName: '创建者',
        status: 'active',
        images: [],
        activityType: 'lure',
        groupChatId: 'test_group_id',
        created: DateTime.now(),
        updated: DateTime.now(),
      );

      when(mockService.getActivityById(activityId))
          .thenAnswer((_) async => mockActivity);

      // Act & Assert
      expect(
        () => mockService.kickParticipant(activityId, participantUserId, creatorUserId),
        returnsNormally,
      );
    });

    test('非创建者不能踢出参与者', () async {
      // Arrange
      const activityId = 'test_activity_id';
      const participantUserId = 'participant_user_id';
      const nonCreatorUserId = 'non_creator_user_id';
      const creatorUserId = 'creator_user_id';
      
      final mockActivity = FishingActivity(
        id: activityId,
        title: '测试活动',
        description: '测试描述',
        location: {'lat': 39.9042, 'lon': 116.4074},
        startTime: DateTime.now().add(const Duration(hours: 1)),
        duration: 2.0,
        maxParticipants: 10,
        currentParticipants: 2,
        creatorId: creatorUserId,
        creatorName: '创建者',
        status: 'active',
        images: [],
        activityType: 'lure',
        groupChatId: 'test_group_id',
        created: DateTime.now(),
        updated: DateTime.now(),
      );

      when(mockService.getActivityById(activityId))
          .thenAnswer((_) async => mockActivity);

      when(mockService.kickParticipant(activityId, participantUserId, nonCreatorUserId))
          .thenThrow(Exception('只有活动创建者可以管理参与者'));

      // Act & Assert
      expect(
        () => mockService.kickParticipant(activityId, participantUserId, nonCreatorUserId),
        throwsException,
      );
    });

    test('被踢用户不能重新加入活动', () async {
      // Arrange
      const activityId = 'test_activity_id';
      const kickedUserId = 'kicked_user_id';

      when(mockService.joinActivity(activityId, kickedUserId))
          .thenThrow(Exception('您已被移出此活动，无法重新加入'));

      // Act & Assert
      expect(
        () => mockService.joinActivity(activityId, kickedUserId),
        throwsException,
      );
    });

    test('创建者不能踢出自己', () async {
      // Arrange
      const activityId = 'test_activity_id';
      const creatorUserId = 'creator_user_id';
      
      final mockActivity = FishingActivity(
        id: activityId,
        title: '测试活动',
        description: '测试描述',
        location: {'lat': 39.9042, 'lon': 116.4074},
        startTime: DateTime.now().add(const Duration(hours: 1)),
        duration: 2.0,
        maxParticipants: 10,
        currentParticipants: 1,
        creatorId: creatorUserId,
        creatorName: '创建者',
        status: 'active',
        images: [],
        activityType: 'lure',
        groupChatId: 'test_group_id',
        created: DateTime.now(),
        updated: DateTime.now(),
      );

      when(mockService.getActivityById(activityId))
          .thenAnswer((_) async => mockActivity);

      when(mockService.kickParticipant(activityId, creatorUserId, creatorUserId))
          .thenThrow(Exception('不能踢出自己'));

      // Act & Assert
      expect(
        () => mockService.kickParticipant(activityId, creatorUserId, creatorUserId),
        throwsException,
      );
    });
  });
}
