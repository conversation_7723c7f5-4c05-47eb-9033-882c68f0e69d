# 钓点活动过滤系统技术文档

## 文档信息
- **版本**: v1.0
- **创建日期**: 2024年12月
- **模块**: 钓点活动加载、缓存、过滤、显示系统
- **技术栈**: Flutter, Dart, PocketBase

---

## 1. 系统概述

### 1.1 功能概述
钓点活动过滤系统是一个高性能的地理数据管理系统，支持用户自定义过滤条件，实现钓点和活动的智能加载、多级缓存、实时过滤和差异化显示。

### 1.2 核心特性
- **智能过滤**: 支持8种独立过滤条件
- **多级缓存**: 内存缓存 + 本地存储 + 服务端缓存
- **差异化显示**: 不同类型使用不同标记样式和颜色
- **高性能加载**: 增量加载 + 防抖机制
- **实时更新**: 过滤条件变更时智能刷新

### 1.3 技术架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户界面层     │    │   过滤控制层     │    │   数据显示层     │
│  FilterPanel    │────│ FilterService   │────│  MapMarkers     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   缓存管理层     │    │   数据加载层     │    │   标记渲染层     │
│  CacheManager   │────│  DataLoader     │────│ MarkerRenderer  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   本地存储层     │    │   网络请求层     │    │   地图引擎层     │
│ SharedPrefs     │    │  PocketBase     │    │  FlutterMap     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

---

## 2. 过滤系统设计

### 2.1 过滤条件定义

#### 2.1.1 过滤条件枚举
```dart
/// 过滤条件类型
enum FilterType {
  // 基础显示控制
  showSpots,           // 是否显示钓点
  showActivities,      // 是否显示活动
  
  // 活动类型过滤
  showWildFishing,     // 是否显示野钓活动
  showLureFishing,     // 是否显示路亚活动
  showTraditionFishing, // 是否显示台钓活动
  showPondFishing,     // 是否显示收费鱼塘钓鱼活动
  
  // 钓点类型过滤
  showWildSpots,       // 是否显示野钓钓点
  showPondSpots,       // 是否显示鱼塘钓点
  showLureSpots,       // 是否显示路亚钓点
  showSeaSpots,        // 是否显示海钓钓点
  
  // 质量过滤
  showLowQuality,      // 是否显示低质量钓点/活动
}
```

#### 2.1.2 过滤配置模型
```dart
/// 过滤配置类
class FilterConfig {
  // 基础显示控制
  final bool showSpots;
  final bool showActivities;
  
  // 活动类型过滤
  final bool showWildFishing;
  final bool showLureFishing;
  final bool showTraditionFishing;
  final bool showPondFishing;
  
  // 钓点类型过滤
  final bool showWildSpots;
  final bool showPondSpots;
  final bool showLureSpots;
  final bool showSeaSpots;
  
  // 质量过滤
  final bool showLowQuality;
  
  const FilterConfig({
    this.showSpots = true,
    this.showActivities = true,
    this.showWildFishing = true,
    this.showLureFishing = true,
    this.showTraditionFishing = true,
    this.showPondFishing = true,
    this.showWildSpots = true,
    this.showPondSpots = true,
    this.showLureSpots = true,
    this.showSeaSpots = true,
    this.showLowQuality = false,
  });
  
  /// 生成缓存键
  String get cacheKey {
    return [
      showSpots ? '1' : '0',
      showActivities ? '1' : '0',
      showWildFishing ? '1' : '0',
      showLureFishing ? '1' : '0',
      showTraditionFishing ? '1' : '0',
      showPondFishing ? '1' : '0',
      showWildSpots ? '1' : '0',
      showPondSpots ? '1' : '0',
      showLureSpots ? '1' : '0',
      showSeaSpots ? '1' : '0',
      showLowQuality ? '1' : '0',
    ].join('');
  }
  
  /// 转换为服务端查询条件
  Map<String, dynamic> toQueryParams() {
    final params = <String, dynamic>{};
    
    // 钓点类型过滤
    if (showSpots) {
      final spotTypes = <String>[];
      if (showWildSpots) spotTypes.add('wild');
      if (showPondSpots) spotTypes.add('pond');
      if (showLureSpots) spotTypes.add('lure');
      if (showSeaSpots) spotTypes.add('sea');
      
      if (spotTypes.isNotEmpty) {
        params['spot_types'] = spotTypes.join(',');
      }
    }
    
    // 活动类型过滤
    if (showActivities) {
      final activityTypes = <String>[];
      if (showWildFishing) activityTypes.add('wild');
      if (showLureFishing) activityTypes.add('lure');
      if (showTraditionFishing) activityTypes.add('tradition');
      if (showPondFishing) activityTypes.add('pond');
      
      if (activityTypes.isNotEmpty) {
        params['activity_types'] = activityTypes.join(',');
      }
    }
    
    // 质量过滤
    params['include_low_quality'] = showLowQuality;
    
    return params;
  }
}
```

### 2.2 过滤服务实现

#### 2.2.1 过滤服务类
```dart
/// 过滤服务
class FilterService extends ChangeNotifier {
  static final FilterService _instance = FilterService._internal();
  factory FilterService() => _instance;
  FilterService._internal();
  
  // 当前过滤配置
  FilterConfig _currentConfig = const FilterConfig();
  FilterConfig get currentConfig => _currentConfig;
  
  // 过滤配置存储键
  static const String _configStorageKey = 'filter_config';
  
  /// 初始化过滤服务
  Future<void> initialize() async {
    await _loadConfigFromLocal();
  }
  
  /// 更新过滤配置
  Future<void> updateConfig(FilterConfig newConfig) async {
    if (_currentConfig != newConfig) {
      _currentConfig = newConfig;
      await _saveConfigToLocal();
      notifyListeners();
    }
  }
  
  /// 更新单个过滤条件
  Future<void> updateFilter(FilterType type, bool value) async {
    FilterConfig newConfig;
    
    switch (type) {
      case FilterType.showSpots:
        newConfig = _currentConfig.copyWith(showSpots: value);
        break;
      case FilterType.showActivities:
        newConfig = _currentConfig.copyWith(showActivities: value);
        break;
      case FilterType.showWildFishing:
        newConfig = _currentConfig.copyWith(showWildFishing: value);
        break;
      case FilterType.showLureFishing:
        newConfig = _currentConfig.copyWith(showLureFishing: value);
        break;
      case FilterType.showTraditionFishing:
        newConfig = _currentConfig.copyWith(showTraditionFishing: value);
        break;
      case FilterType.showPondFishing:
        newConfig = _currentConfig.copyWith(showPondFishing: value);
        break;
      case FilterType.showWildSpots:
        newConfig = _currentConfig.copyWith(showWildSpots: value);
        break;
      case FilterType.showPondSpots:
        newConfig = _currentConfig.copyWith(showPondSpots: value);
        break;
      case FilterType.showLureSpots:
        newConfig = _currentConfig.copyWith(showLureSpots: value);
        break;
      case FilterType.showSeaSpots:
        newConfig = _currentConfig.copyWith(showSeaSpots: value);
        break;
      case FilterType.showLowQuality:
        newConfig = _currentConfig.copyWith(showLowQuality: value);
        break;
    }
    
    await updateConfig(newConfig);
  }
  
  /// 重置为默认配置
  Future<void> resetToDefault() async {
    await updateConfig(const FilterConfig());
  }
  
  /// 从本地存储加载配置
  Future<void> _loadConfigFromLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configJson = prefs.getString(_configStorageKey);
      
      if (configJson != null) {
        final configMap = jsonDecode(configJson);
        _currentConfig = FilterConfig.fromJson(configMap);
      }
    } catch (e) {
      debugPrint('加载过滤配置失败: $e');
      _currentConfig = const FilterConfig();
    }
  }
  
  /// 保存配置到本地存储
  Future<void> _saveConfigToLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configJson = jsonEncode(_currentConfig.toJson());
      await prefs.setString(_configStorageKey, configJson);
    } catch (e) {
      debugPrint('保存过滤配置失败: $e');
    }
  }
}
```

---

## 3. 缓存系统设计

### 3.1 多级缓存架构

#### 3.1.1 缓存层级结构
```
L1 缓存 (内存缓存)
├── 钓点数据缓存 (5分钟TTL)
├── 活动数据缓存 (3分钟TTL)
├── 过滤结果缓存 (1分钟TTL)
└── 标记组件缓存 (会话级别)

L2 缓存 (本地存储)
├── 钓点数据持久化
├── 活动数据持久化
├── 过滤配置持久化
└── 图片资源缓存

L3 缓存 (服务端缓存)
├── 地理区域查询缓存
├── 用户权限缓存
└── 热点数据缓存
```

#### 3.1.2 缓存管理器
```dart
/// 缓存管理器
class CacheManager {
  static final CacheManager _instance = CacheManager._internal();
  factory CacheManager() => _instance;
  CacheManager._internal();
  
  // L1缓存 - 内存缓存
  final Map<String, CacheEntry<List<FishingSpot>>> _spotCache = {};
  final Map<String, CacheEntry<List<FishingActivity>>> _activityCache = {};
  final Map<String, CacheEntry<FilteredData>> _filteredCache = {};
  
  // 缓存TTL配置
  static const Duration _spotCacheTTL = Duration(minutes: 5);
  static const Duration _activityCacheTTL = Duration(minutes: 3);
  static const Duration _filteredCacheTTL = Duration(minutes: 1);
  
  /// 获取钓点缓存
  List<FishingSpot>? getCachedSpots(String regionKey, FilterConfig config) {
    final cacheKey = '${regionKey}_${config.cacheKey}';
    final entry = _spotCache[cacheKey];
    
    if (entry != null && !entry.isExpired) {
      debugPrint('🎯 [缓存命中] 钓点数据: $cacheKey');
      return entry.data;
    }
    
    return null;
  }
  
  /// 缓存钓点数据
  void cacheSpots(String regionKey, FilterConfig config, List<FishingSpot> spots) {
    final cacheKey = '${regionKey}_${config.cacheKey}';
    _spotCache[cacheKey] = CacheEntry(
      data: spots,
      timestamp: DateTime.now(),
      ttl: _spotCacheTTL,
    );
    
    debugPrint('💾 [缓存存储] 钓点数据: $cacheKey, 数量: ${spots.length}');
  }
  
  /// 获取活动缓存
  List<FishingActivity>? getCachedActivities(String regionKey, FilterConfig config) {
    final cacheKey = '${regionKey}_${config.cacheKey}';
    final entry = _activityCache[cacheKey];
    
    if (entry != null && !entry.isExpired) {
      debugPrint('🎯 [缓存命中] 活动数据: $cacheKey');
      return entry.data;
    }
    
    return null;
  }
  
  /// 缓存活动数据
  void cacheActivities(String regionKey, FilterConfig config, List<FishingActivity> activities) {
    final cacheKey = '${regionKey}_${config.cacheKey}';
    _activityCache[cacheKey] = CacheEntry(
      data: activities,
      timestamp: DateTime.now(),
      ttl: _activityCacheTTL,
    );
    
    debugPrint('💾 [缓存存储] 活动数据: $cacheKey, 数量: ${activities.length}');
  }
  
  /// 清理过期缓存
  void cleanExpiredCache() {
    final now = DateTime.now();
    
    _spotCache.removeWhere((key, entry) => entry.isExpired);
    _activityCache.removeWhere((key, entry) => entry.isExpired);
    _filteredCache.removeWhere((key, entry) => entry.isExpired);
    
    debugPrint('🧹 [缓存清理] 已清理过期缓存');
  }
  
  /// 清理所有缓存
  void clearAllCache() {
    _spotCache.clear();
    _activityCache.clear();
    _filteredCache.clear();
    
    debugPrint('🧹 [缓存清理] 已清理所有缓存');
  }
}

/// 缓存条目
class CacheEntry<T> {
  final T data;
  final DateTime timestamp;
  final Duration ttl;
  
  CacheEntry({
    required this.data,
    required this.timestamp,
    required this.ttl,
  });
  
  bool get isExpired => DateTime.now().difference(timestamp) > ttl;
}
```

---

## 4. 数据加载系统

### 4.1 智能加载策略

#### 4.1.1 数据加载器
```dart
/// 数据加载器
class DataLoader {
  static final DataLoader _instance = DataLoader._internal();
  factory DataLoader() => _instance;
  DataLoader._internal();
  
  final CacheManager _cacheManager = CacheManager();
  final FilterService _filterService = FilterService();
  
  // 防抖控制
  Timer? _loadTimer;
  static const Duration _debounceDelay = Duration(milliseconds: 500);
  
  // 加载状态
  bool _isLoading = false;
  String? _lastRegionKey;
  
  /// 加载过滤后的数据
  Future<FilteredData> loadFilteredData({
    required LatLng center,
    required double radiusKm,
    bool forceRefresh = false,
  }) async {
    final regionKey = _generateRegionKey(center, radiusKm);
    final config = _filterService.currentConfig;
    
    // 检查缓存
    if (!forceRefresh) {
      final cachedSpots = _cacheManager.getCachedSpots(regionKey, config);
      final cachedActivities = _cacheManager.getCachedActivities(regionKey, config);
      
      if (cachedSpots != null && cachedActivities != null) {
        return FilteredData(
          spots: cachedSpots,
          activities: cachedActivities,
          fromCache: true,
        );
      }
    }
    
    // 防抖处理
    if (_loadTimer != null) {
      _loadTimer!.cancel();
    }
    
    final completer = Completer<FilteredData>();
    
    _loadTimer = Timer(_debounceDelay, () async {
      try {
        final data = await _performLoad(center, radiusKm, config);
        completer.complete(data);
      } catch (e) {
        completer.completeError(e);
      }
    });
    
    return completer.future;
  }
  
  /// 执行实际加载
  Future<FilteredData> _performLoad(
    LatLng center,
    double radiusKm,
    FilterConfig config,
  ) async {
    if (_isLoading) {
      throw Exception('数据正在加载中');
    }
    
    _isLoading = true;
    
    try {
      debugPrint('🔄 [数据加载] 开始加载: center=$center, radius=$radiusKm');
      
      // 并行加载钓点和活动
      final futures = <Future>[];
      
      if (config.showSpots) {
        futures.add(_loadSpots(center, radiusKm, config));
      }
      
      if (config.showActivities) {
        futures.add(_loadActivities(center, radiusKm, config));
      }
      
      final results = await Future.wait(futures);
      
      final spots = config.showSpots ? results[0] as List<FishingSpot> : <FishingSpot>[];
      final activities = config.showActivities 
          ? results[config.showSpots ? 1 : 0] as List<FishingActivity>
          : <FishingActivity>[];
      
      // 缓存结果
      final regionKey = _generateRegionKey(center, radiusKm);
      _cacheManager.cacheSpots(regionKey, config, spots);
      _cacheManager.cacheActivities(regionKey, config, activities);
      
      debugPrint('✅ [数据加载] 完成: 钓点=${spots.length}, 活动=${activities.length}');
      
      return FilteredData(
        spots: spots,
        activities: activities,
        fromCache: false,
      );
    } finally {
      _isLoading = false;
    }
  }
  
  /// 加载钓点数据
  Future<List<FishingSpot>> _loadSpots(
    LatLng center,
    double radiusKm,
    FilterConfig config,
  ) async {
    final queryParams = config.toQueryParams();
    queryParams['center_lat'] = center.latitude;
    queryParams['center_lng'] = center.longitude;
    queryParams['radius_km'] = radiusKm;
    
    // 构建过滤条件
    final filters = <String>[];
    
    // 钓点类型过滤
    if (queryParams.containsKey('spot_types')) {
      final types = queryParams['spot_types'].split(',');
      final typeFilter = types.map((type) => "spot_type = '$type'").join(' || ');
      filters.add('($typeFilter)');
    }
    
    // 质量过滤
    if (!config.showLowQuality) {
      filters.add('quality_score >= 3.0');
    }
    
    // 地理位置过滤
    filters.add(
      'geoDistance(location.lon, location.lat, ${center.longitude}, ${center.latitude}) <= $radiusKm'
    );
    
    final filterString = filters.join(' && ');
    
    try {
      final records = await pb.collection('fishing_spots').getList(
        page: 1,
        perPage: 200,
        filter: filterString,
        sort: '-created',
        expand: 'user_id,spot_photos_via_spot_id',
      );
      
      return _convertRecordsToSpots(records.items);
    } catch (e) {
      debugPrint('❌ [钓点加载] 失败: $e');
      return [];
    }
  }
  
  /// 加载活动数据
  Future<List<FishingActivity>> _loadActivities(
    LatLng center,
    double radiusKm,
    FilterConfig config,
  ) async {
    final queryParams = config.toQueryParams();
    
    // 构建过滤条件
    final filters = <String>[];
    
    // 活动类型过滤
    if (queryParams.containsKey('activity_types')) {
      final types = queryParams['activity_types'].split(',');
      final typeFilter = types.map((type) => "activity_type = '$type'").join(' || ');
      filters.add('($typeFilter)');
    }
    
    // 质量过滤
    if (!config.showLowQuality) {
      filters.add('quality_score >= 3.0');
    }
    
    // 时间过滤（只显示未结束的活动）
    filters.add('end_time >= @now');
    
    // 地理位置过滤
    filters.add(
      'geoDistance(location.lon, location.lat, ${center.longitude}, ${center.latitude}) <= $radiusKm'
    );
    
    final filterString = filters.join(' && ');
    
    try {
      final records = await pb.collection('fishing_activities').getList(
        page: 1,
        perPage: 100,
        filter: filterString,
        sort: 'start_time',
        expand: 'creator_id',
      );
      
      return _convertRecordsToActivities(records.items);
    } catch (e) {
      debugPrint('❌ [活动加载] 失败: $e');
      return [];
    }
  }
  
  /// 生成区域缓存键
  String _generateRegionKey(LatLng center, double radiusKm) {
    final lat = (center.latitude * 1000).round() / 1000;
    final lng = (center.longitude * 1000).round() / 1000;
    final radius = (radiusKm * 10).round() / 10;
    return '${lat}_${lng}_$radius';
  }
}

/// 过滤后的数据
class FilteredData {
  final List<FishingSpot> spots;
  final List<FishingActivity> activities;
  final bool fromCache;
  
  const FilteredData({
    required this.spots,
    required this.activities,
    required this.fromCache,
  });
  
  int get totalCount => spots.length + activities.length;
}
```

---

## 5. 标记显示系统

### 5.1 标记样式设计

#### 5.1.1 标记类型定义
```dart
/// 标记类型枚举
enum MarkerType {
  spot,      // 钓点标记
  activity,  // 活动标记
}

/// 钓点类型枚举
enum SpotType {
  wild,      // 野钓
  pond,      // 鱼塘
  lure,      // 路亚
  sea,       // 海钓
}

/// 活动类型枚举
enum ActivityType {
  wild,      // 野钓活动
  lure,      // 路亚活动
  tradition, // 台钓活动
  pond,      // 鱼塘活动
}
```

#### 5.1.2 标记样式配置
```dart
/// 标记样式配置
class MarkerStyleConfig {
  // 钓点标记样式 - 圆形 + 三角
  static const Map<SpotType, MarkerStyle> spotStyles = {
    SpotType.wild: MarkerStyle(
      shape: MarkerShape.circleWithTriangle,
      borderColor: Color(0xFF4CAF50),  // 绿色边框 - 野钓
      fillColor: Color(0xFFE8F5E8),
      borderWidth: 3.0,
      size: 60.0,
    ),
    SpotType.pond: MarkerStyle(
      shape: MarkerShape.circleWithTriangle,
      borderColor: Color(0xFF2196F3),  // 蓝色边框 - 鱼塘
      fillColor: Color(0xFFE3F2FD),
      borderWidth: 3.0,
      size: 60.0,
    ),
    SpotType.lure: MarkerStyle(
      shape: MarkerShape.circleWithTriangle,
      borderColor: Color(0xFFFF9800),  // 橙色边框 - 路亚
      fillColor: Color(0xFFFFF3E0),
      borderWidth: 3.0,
      size: 60.0,
    ),
    SpotType.sea: MarkerStyle(
      shape: MarkerShape.circleWithTriangle,
      borderColor: Color(0xFF00BCD4),  // 青色边框 - 海钓
      fillColor: Color(0xFFE0F2F1),
      borderWidth: 3.0,
      size: 60.0,
    ),
  };
  
  // 活动标记样式 - 方形 + 三角
  static const Map<ActivityType, MarkerStyle> activityStyles = {
    ActivityType.wild: MarkerStyle(
      shape: MarkerShape.squareWithTriangle,
      borderColor: Color(0xFF4CAF50),  // 绿色边框 - 野钓活动
      fillColor: Color(0xFFE8F5E8),
      borderWidth: 3.0,
      size: 55.0,
    ),
    ActivityType.lure: MarkerStyle(
      shape: MarkerShape.squareWithTriangle,
      borderColor: Color(0xFFFF9800),  // 橙色边框 - 路亚活动
      fillColor: Color(0xFFFFF3E0),
      borderWidth: 3.0,
      size: 55.0,
    ),
    ActivityType.tradition: MarkerStyle(
      shape: MarkerShape.squareWithTriangle,
      borderColor: Color(0xFF9C27B0),  // 紫色边框 - 台钓活动
      fillColor: Color(0xFFF3E5F5),
      borderWidth: 3.0,
      size: 55.0,
    ),
    ActivityType.pond: MarkerStyle(
      shape: MarkerShape.squareWithTriangle,
      borderColor: Color(0xFF2196F3),  // 蓝色边框 - 鱼塘活动
      fillColor: Color(0xFFE3F2FD),
      borderWidth: 3.0,
      size: 55.0,
    ),
  };
}

/// 标记样式
class MarkerStyle {
  final MarkerShape shape;
  final Color borderColor;
  final Color fillColor;
  final double borderWidth;
  final double size;
  
  const MarkerStyle({
    required this.shape,
    required this.borderColor,
    required this.fillColor,
    required this.borderWidth,
    required this.size,
  });
}

/// 标记形状
enum MarkerShape {
  circleWithTriangle,   // 圆形 + 三角 (钓点)
  squareWithTriangle,   // 方形 + 三角 (活动)
}
```

### 5.2 标记渲染器

#### 5.2.1 自定义标记组件
```dart
/// 过滤标记组件
class FilteredMarker extends StatefulWidget {
  final MarkerType type;
  final dynamic data; // FishingSpot 或 FishingActivity
  final VoidCallback? onTap;
  
  const FilteredMarker({
    super.key,
    required this.type,
    required this.data,
    this.onTap,
  });
  
  @override
  State<FilteredMarker> createState() => _FilteredMarkerState();
}

class _FilteredMarkerState extends State<FilteredMarker> 
    with AutomaticKeepAliveClientMixin {
  
  @override
  bool get wantKeepAlive => true;
  
  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    final style = _getMarkerStyle();
    
    return GestureDetector(
      onTap: widget.onTap,
      child: CustomPaint(
        size: Size(style.size, style.size * 1.2), // 为三角留出空间
        painter: MarkerPainter(
          style: style,
          hasPhoto: _hasPhoto(),
          photoUrl: _getPhotoUrl(),
          qualityScore: _getQualityScore(),
        ),
      ),
    );
  }
  
  /// 获取标记样式
  MarkerStyle _getMarkerStyle() {
    switch (widget.type) {
      case MarkerType.spot:
        final spot = widget.data as FishingSpot;
        final spotType = _parseSpotType(spot.spotType);
        return MarkerStyleConfig.spotStyles[spotType] ?? 
               MarkerStyleConfig.spotStyles[SpotType.wild]!;
               
      case MarkerType.activity:
        final activity = widget.data as FishingActivity;
        final activityType = _parseActivityType(activity.activityType);
        return MarkerStyleConfig.activityStyles[activityType] ?? 
               MarkerStyleConfig.activityStyles[ActivityType.wild]!;
    }
  }
  
  /// 解析钓点类型
  SpotType _parseSpotType(String? type) {
    switch (type?.toLowerCase()) {
      case 'wild': return SpotType.wild;
      case 'pond': return SpotType.pond;
      case 'lure': return SpotType.lure;
      case 'sea': return SpotType.sea;
      default: return SpotType.wild;
    }
  }
  
  /// 解析活动类型
  ActivityType _parseActivityType(String? type) {
    switch (type?.toLowerCase()) {
      case 'wild': return ActivityType.wild;
      case 'lure': return ActivityType.lure;
      case 'tradition': return ActivityType.tradition;
      case 'pond': return ActivityType.pond;
      default: return ActivityType.wild;
    }
  }
  
  bool _hasPhoto() {
    if (widget.type == MarkerType.spot) {
      final spot = widget.data as FishingSpot;
      return spot.photoUrls.isNotEmpty;
    }
    return false;
  }
  
  String? _getPhotoUrl() {
    if (widget.type == MarkerType.spot) {
      final spot = widget.data as FishingSpot;
      return spot.photoUrls.isNotEmpty ? spot.photoUrls.first : null;
    }
    return null;
  }
  
  double _getQualityScore() {
    if (widget.type == MarkerType.spot) {
      final spot = widget.data as FishingSpot;
      return spot.qualityScore ?? 3.0;
    } else {
      final activity = widget.data as FishingActivity;
      return activity.qualityScore ?? 3.0;
    }
  }
}
```

#### 5.2.2 标记绘制器
```dart
/// 标记绘制器
class MarkerPainter extends CustomPainter {
  final MarkerStyle style;
  final bool hasPhoto;
  final String? photoUrl;
  final double qualityScore;
  
  MarkerPainter({
    required this.style,
    required this.hasPhoto,
    this.photoUrl,
    required this.qualityScore,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..color = style.fillColor;
      
    final borderPaint = Paint()
      ..style = PaintingStyle.stroke
      ..color = style.borderColor
      ..strokeWidth = style.borderWidth;
    
    final center = Offset(size.width / 2, size.height * 0.4);
    final radius = (size.width - style.borderWidth) / 2;
    
    // 绘制主体形状
    switch (style.shape) {
      case MarkerShape.circleWithTriangle:
        _drawCircleWithTriangle(canvas, center, radius, paint, borderPaint);
        break;
      case MarkerShape.squareWithTriangle:
        _drawSquareWithTriangle(canvas, center, radius, paint, borderPaint);
        break;
    }
    
    // 绘制质量指示器
    if (qualityScore < 3.0) {
      _drawQualityIndicator(canvas, center, radius);
    }
    
    // 绘制照片或图标
    if (hasPhoto && photoUrl != null) {
      _drawPhoto(canvas, center, radius);
    } else {
      _drawDefaultIcon(canvas, center, radius);
    }
  }
  
  /// 绘制圆形 + 三角
  void _drawCircleWithTriangle(
    Canvas canvas, 
    Offset center, 
    double radius,
    Paint fillPaint,
    Paint borderPaint,
  ) {
    // 绘制圆形
    canvas.drawCircle(center, radius, fillPaint);
    canvas.drawCircle(center, radius, borderPaint);
    
    // 绘制底部三角
    final trianglePath = Path();
    final triangleTop = Offset(center.dx, center.dy + radius);
    final triangleLeft = Offset(center.dx - radius * 0.3, center.dy + radius);
    final triangleRight = Offset(center.dx + radius * 0.3, center.dy + radius);
    final triangleBottom = Offset(center.dx, center.dy + radius * 1.5);
    
    trianglePath.moveTo(triangleLeft.dx, triangleLeft.dy);
    trianglePath.lineTo(triangleRight.dx, triangleRight.dy);
    trianglePath.lineTo(triangleBottom.dx, triangleBottom.dy);
    trianglePath.close();
    
    canvas.drawPath(trianglePath, fillPaint);
    canvas.drawPath(trianglePath, borderPaint);
  }
  
  /// 绘制方形 + 三角
  void _drawSquareWithTriangle(
    Canvas canvas, 
    Offset center, 
    double radius,
    Paint fillPaint,
    Paint borderPaint,
  ) {
    // 绘制方形
    final rect = Rect.fromCenter(
      center: center,
      width: radius * 2,
      height: radius * 2,
    );
    
    canvas.drawRect(rect, fillPaint);
    canvas.drawRect(rect, borderPaint);
    
    // 绘制底部三角（与圆形相同）
    final trianglePath = Path();
    final triangleTop = Offset(center.dx, center.dy + radius);
    final triangleLeft = Offset(center.dx - radius * 0.3, center.dy + radius);
    final triangleRight = Offset(center.dx + radius * 0.3, center.dy + radius);
    final triangleBottom = Offset(center.dx, center.dy + radius * 1.5);
    
    trianglePath.moveTo(triangleLeft.dx, triangleLeft.dy);
    trianglePath.lineTo(triangleRight.dx, triangleRight.dy);
    trianglePath.lineTo(triangleBottom.dx, triangleBottom.dy);
    trianglePath.close();
    
    canvas.drawPath(trianglePath, fillPaint);
    canvas.drawPath(trianglePath, borderPaint);
  }
  
  /// 绘制质量指示器
  void _drawQualityIndicator(Canvas canvas, Offset center, double radius) {
    final warningPaint = Paint()
      ..color = Colors.orange
      ..style = PaintingStyle.fill;
      
    final warningRadius = radius * 0.2;
    final warningCenter = Offset(
      center.dx + radius * 0.7,
      center.dy - radius * 0.7,
    );
    
    canvas.drawCircle(warningCenter, warningRadius, warningPaint);
    
    // 绘制感叹号
    final textPainter = TextPainter(
      text: TextSpan(
        text: '!',
        style: TextStyle(
          color: Colors.white,
          fontSize: warningRadius,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        warningCenter.dx - textPainter.width / 2,
        warningCenter.dy - textPainter.height / 2,
      ),
    );
  }
  
  @override
  bool shouldRepaint(covariant MarkerPainter oldDelegate) {
    return oldDelegate.style != style ||
           oldDelegate.hasPhoto != hasPhoto ||
           oldDelegate.photoUrl != photoUrl ||
           oldDelegate.qualityScore != qualityScore;
  }
}
```

---

## 6. 性能优化策略

### 6.1 渲染性能优化

#### 6.1.1 标记池化管理
```dart
/// 标记池化管理器
class MarkerPoolManager {
  static final MarkerPoolManager _instance = MarkerPoolManager._internal();
  factory MarkerPoolManager() => _instance;
  MarkerPoolManager._internal();
  
  // 标记对象池
  final Map<String, Queue<FilteredMarker>> _markerPools = {};
  final Map<String, FilteredMarker> _activeMarkers = {};
  
  // 池化配置
  static const int _maxPoolSize = 50;
  static const int _initialPoolSize = 10;
  
  /// 获取标记实例
  FilteredMarker getMarker({
    required String id,
    required MarkerType type,
    required dynamic data,
    VoidCallback? onTap,
  }) {
    final poolKey = '${type.name}_${_getDataType(data)}';
    
    // 检查是否已有活跃标记
    if (_activeMarkers.containsKey(id)) {
      return _activeMarkers[id]!;
    }
    
    // 从对象池获取
    final pool = _markerPools[poolKey];
    FilteredMarker marker;
    
    if (pool != null && pool.isNotEmpty) {
      marker = pool.removeFirst();
      // 更新标记数据
      marker = FilteredMarker(
        key: ValueKey(id),
        type: type,
        data: data,
        onTap: onTap,
      );
    } else {
      // 创建新标记
      marker = FilteredMarker(
        key: ValueKey(id),
        type: type,
        data: data,
        onTap: onTap,
      );
    }
    
    _activeMarkers[id] = marker;
    return marker;
  }
  
  /// 回收标记实例
  void recycleMarker(String id) {
    final marker = _activeMarkers.remove(id);
    if (marker == null) return;
    
    final poolKey = '${marker.type.name}_${_getDataType(marker.data)}';
    final pool = _markerPools.putIfAbsent(poolKey, () => Queue<FilteredMarker>());
    
    if (pool.length < _maxPoolSize) {
      pool.add(marker);
    }
  }
  
  String _getDataType(dynamic data) {
    if (data is FishingSpot) {
      return data.spotType ?? 'unknown';
    } else if (data is FishingActivity) {
      return data.activityType ?? 'unknown';
    }
    return 'unknown';
  }
}
```

#### 6.1.2 批量渲染优化
```dart
/// 批量渲染管理器
class BatchRenderManager {
  static final BatchRenderManager _instance = BatchRenderManager._internal();
  factory BatchRenderManager() => _instance;
  BatchRenderManager._internal();
  
  Timer? _renderTimer;
  final List<RenderTask> _pendingTasks = [];
  
  static const Duration _batchDelay = Duration(milliseconds: 16); // 60fps
  
  /// 添加渲染任务
  void addRenderTask(RenderTask task) {
    _pendingTasks.add(task);
    
    _renderTimer?.cancel();
    _renderTimer = Timer(_batchDelay, _processBatch);
  }
  
  /// 处理批量渲染
  void _processBatch() {
    if (_pendingTasks.isEmpty) return;
    
    final tasks = List<RenderTask>.from(_pendingTasks);
    _pendingTasks.clear();
    
    // 按优先级排序
    tasks.sort((a, b) => b.priority.compareTo(a.priority));
    
    // 分批处理，避免阻塞UI
    _processTasksInChunks(tasks);
  }
  
  /// 分块处理任务
  void _processTasksInChunks(List<RenderTask> tasks) {
    const chunkSize = 20;
    int index = 0;
    
    void processNextChunk() {
      final endIndex = math.min(index + chunkSize, tasks.length);
      
      for (int i = index; i < endIndex; i++) {
        tasks[i].execute();
      }
      
      index = endIndex;
      
      if (index < tasks.length) {
        // 下一帧继续处理
        WidgetsBinding.instance.addPostFrameCallback((_) {
          processNextChunk();
        });
      }
    }
    
    processNextChunk();
  }
}

/// 渲染任务
class RenderTask {
  final String id;
  final int priority;
  final VoidCallback execute;
  
  RenderTask({
    required this.id,
    required this.priority,
    required this.execute,
  });
}
```

### 6.2 内存优化

#### 6.2.1 智能缓存清理
```dart
/// 智能缓存清理器
class SmartCacheCleaner {
  static final SmartCacheCleaner _instance = SmartCacheCleaner._internal();
  factory SmartCacheCleaner() => _instance;
  SmartCacheCleaner._internal();
  
  Timer? _cleanupTimer;
  static const Duration _cleanupInterval = Duration(minutes: 2);
  
  /// 启动智能清理
  void startSmartCleanup() {
    _cleanupTimer?.cancel();
    _cleanupTimer = Timer.periodic(_cleanupInterval, (_) => _performCleanup());
  }
  
  /// 停止智能清理
  void stopSmartCleanup() {
    _cleanupTimer?.cancel();
  }
  
  /// 执行清理
  void _performCleanup() {
    final memoryInfo = _getMemoryInfo();
    
    if (memoryInfo.usedMemoryMB > 80) { // 超过80MB时清理
      debugPrint('🧹 [内存清理] 当前内存使用: ${memoryInfo.usedMemoryMB}MB');
      
      // 清理过期缓存
      CacheManager().cleanExpiredCache();
      
      // 清理不可见标记
      _cleanupInvisibleMarkers();
      
      // 清理图片缓存
      _cleanupImageCache();
      
      // 强制垃圾回收
      _forceGarbageCollection();
    }
  }
  
  /// 清理不可见标记
  void _cleanupInvisibleMarkers() {
    // 实现标记可见性检测和清理逻辑
  }
  
  /// 清理图片缓存
  void _cleanupImageCache() {
    // 实现图片缓存清理逻辑
  }
  
  /// 强制垃圾回收
  void _forceGarbageCollection() {
    // 触发垃圾回收
  }
  
  MemoryInfo _getMemoryInfo() {
    // 获取内存使用信息
    return MemoryInfo(usedMemoryMB: 50); // 示例数据
  }
}

class MemoryInfo {
  final double usedMemoryMB;
  MemoryInfo({required this.usedMemoryMB});
}
```

---

## 7. API接口设计

### 7.1 过滤查询接口

#### 7.1.1 钓点过滤查询
```http
GET /api/fishing-spots/filtered

Query Parameters:
- center_lat: double (必填) - 中心纬度
- center_lng: double (必填) - 中心经度  
- radius_km: double (必填) - 搜索半径(公里)
- spot_types: string (可选) - 钓点类型，逗号分隔 (wild,pond,lure,sea)
- include_low_quality: boolean (可选) - 是否包含低质量钓点，默认false
- page: int (可选) - 页码，默认1
- per_page: int (可选) - 每页数量，默认50，最大200

Response:
{
  "success": true,
  "data": {
    "spots": [
      {
        "id": "spot_123",
        "name": "西湖钓点",
        "location": {"lat": 30.2741, "lon": 120.1551},
        "spot_type": "wild",
        "quality_score": 4.2,
        "photo_urls": ["url1", "url2"],
        "created": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 156,
    "page": 1,
    "per_page": 50,
    "has_more": true
  }
}
```

#### 7.1.2 活动过滤查询
```http
GET /api/fishing-activities/filtered

Query Parameters:
- center_lat: double (必填) - 中心纬度
- center_lng: double (必填) - 中心经度
- radius_km: double (必填) - 搜索半径(公里)
- activity_types: string (可选) - 活动类型，逗号分隔 (wild,lure,tradition,pond)
- include_low_quality: boolean (可选) - 是否包含低质量活动，默认false
- time_filter: string (可选) - 时间过滤 (upcoming,ongoing,all)，默认upcoming

Response:
{
  "success": true,
  "data": {
    "activities": [
      {
        "id": "activity_456", 
        "title": "周末野钓活动",
        "location": {"lat": 30.2741, "lon": 120.1551},
        "activity_type": "wild",
        "start_time": "2024-12-15T08:00:00Z",
        "end_time": "2024-12-15T18:00:00Z",
        "max_participants": 10,
        "current_participants": 5,
        "quality_score": 4.0
      }
    ],
    "total": 23,
    "has_more": false
  }
}
```

### 7.2 缓存控制接口

#### 7.2.1 缓存状态查询
```http
GET /api/cache/status

Response:
{
  "success": true,
  "data": {
    "cache_hit_rate": 0.85,
    "total_cached_spots": 1250,
    "total_cached_activities": 340,
    "cache_size_mb": 45.2,
    "last_cleanup": "2024-12-01T10:30:00Z"
  }
}
```

---

## 8. 监控和调试

### 8.1 性能监控

#### 8.1.1 性能指标收集
```dart
/// 性能监控器
class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();
  
  final Map<String, PerformanceMetric> _metrics = {};
  
  /// 开始性能测量
  void startMeasurement(String operation) {
    _metrics[operation] = PerformanceMetric(
      operation: operation,
      startTime: DateTime.now(),
    );
  }
  
  /// 结束性能测量
  void endMeasurement(String operation, {Map<String, dynamic>? metadata}) {
    final metric = _metrics[operation];
    if (metric == null) return;
    
    metric.endTime = DateTime.now();
    metric.duration = metric.endTime!.difference(metric.startTime);
    metric.metadata = metadata;
    
    _reportMetric(metric);
  }
  
  /// 上报性能指标
  void _reportMetric(PerformanceMetric metric) {
    debugPrint('📊 [性能监控] ${metric.operation}: ${metric.duration?.inMilliseconds}ms');
    
    // 上报到分析服务
    if (metric.duration!.inMilliseconds > 1000) {
      debugPrint('⚠️ [性能警告] ${metric.operation} 耗时过长');
    }
  }
}

class PerformanceMetric {
  final String operation;
  final DateTime startTime;
  DateTime? endTime;
  Duration? duration;
  Map<String, dynamic>? metadata;
  
  PerformanceMetric({
    required this.operation,
    required this.startTime,
  });
}
```

---

## 9. 总结

### 9.1 系统特点
- **高性能**: 多级缓存 + 增量加载 + 对象池化
- **灵活过滤**: 8种独立过滤条件，支持复杂组合
- **差异化显示**: 不同类型使用不同颜色和形状标记
- **智能优化**: 自动内存管理 + 批量渲染

### 9.2 技术亮点
- 过滤条件变更时智能缓存失效
- 标记组件池化复用，减少GC压力
- 批量渲染避免UI阻塞
- 多维度性能监控和调试

### 9.3 扩展性
- 支持新增过滤条件类型
- 支持自定义标记样式
- 支持插件化扩展
- 支持多数据源适配